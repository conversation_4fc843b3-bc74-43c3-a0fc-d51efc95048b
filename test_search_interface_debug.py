#!/usr/bin/env python3
"""
Test the search interface to see why it's returning dummy data
"""

import sys
import os
sys.path.append('VideoApp')

from PyQt5.QtWidgets import QApplication
from ui.search_interface import SearchThread

def test_search_interface():
    """Test the search interface directly"""
    app = QApplication.instance()
    if app is None:
        app = QApplication([])

    # Create search thread
    search_thread = SearchThread("python tutorial", "YouTube", 5)

    print(f"Testing search with query: {search_thread.query}")
    print(f"Platform: {search_thread.platform}")
    print(f"Max results: {search_thread.max_results}")

    # Call the search method directly
    results = search_thread.search_youtube()
    
    print(f"\nSearch completed!")
    print(f"Number of results: {len(results) if results else 0}")
    
    if results:
        for i, result in enumerate(results):
            print(f"\n{i+1}. {result.get('title', 'No title')}")
            print(f"   Platform: {result.get('platform', 'Unknown')}")
            print(f"   Views: {result.get('views', 'Unknown')}")
            print(f"   Duration: {result.get('duration', 'Unknown')}")
            print(f"   URL: {result.get('url', 'No URL')}")
            
            # Check if this is dummy data
            if result.get('video_id') == 'sample123':
                print("   ⚠️ THIS IS DUMMY DATA!")
            else:
                print("   ✅ This appears to be real data")
    else:
        print("No results returned")

if __name__ == "__main__":
    test_search_interface()
