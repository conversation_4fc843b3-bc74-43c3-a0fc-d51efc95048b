#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script for the font system with English and Urdu fonts
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QLabel, QTextEdit, QHBoxLayout
from PyQt5.QtCore import QTimer
from PyQt5.QtGui import QFont

# Add VideoApp to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'VideoApp'))

try:
    from ui.text_overlay_editor import TextOverlayEditor, TextOverlay
    from utils.font_manager import get_font_manager
    from ui.premiere_interface import PremiereInterface
except ImportError as e:
    print(f"Import error: {e}")
    print("Make sure you're running this from the BISH directory")
    sys.exit(1)

class FontTestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("🎨 Font System Test - English & Urdu")
        self.setGeometry(100, 100, 1000, 700)
        
        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # Title
        title = QLabel("🎨 Font System Test")
        title.setStyleSheet("""
            QLabel {
                font-size: 20px;
                font-weight: bold;
                color: #ffffff;
                padding: 15px;
                background-color: #2d2d30;
                border-radius: 8px;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(title)
        
        # Test log
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(150)
        self.log_text.setStyleSheet("""
            QTextEdit {
                background-color: #1e1e1e;
                color: #ffffff;
                border: 1px solid #555;
                border-radius: 5px;
                font-family: 'Consolas', monospace;
                font-size: 12px;
            }
        """)
        layout.addWidget(self.log_text)
        
        # Test buttons
        button_layout = QHBoxLayout()
        
        test_font_manager_btn = QPushButton("🔤 Test Font Manager")
        test_font_manager_btn.clicked.connect(self.test_font_manager)
        button_layout.addWidget(test_font_manager_btn)
        
        test_text_editor_btn = QPushButton("📝 Test Text Editor")
        test_text_editor_btn.clicked.connect(self.test_text_editor)
        button_layout.addWidget(test_text_editor_btn)
        
        test_urdu_fonts_btn = QPushButton("🕌 Test Urdu Fonts")
        test_urdu_fonts_btn.clicked.connect(self.test_urdu_fonts)
        button_layout.addWidget(test_urdu_fonts_btn)
        
        launch_premiere_btn = QPushButton("🎬 Launch Premiere Interface")
        launch_premiere_btn.clicked.connect(self.launch_premiere_interface)
        button_layout.addWidget(launch_premiere_btn)
        
        # Style buttons
        for btn in [test_font_manager_btn, test_text_editor_btn, test_urdu_fonts_btn, launch_premiere_btn]:
            btn.setStyleSheet("""
                QPushButton {
                    background-color: #0078d4;
                    color: white;
                    border: none;
                    border-radius: 5px;
                    padding: 10px 15px;
                    font-weight: bold;
                    font-size: 12px;
                }
                QPushButton:hover {
                    background-color: #106ebe;
                }
                QPushButton:pressed {
                    background-color: #005a9e;
                }
            """)
        
        layout.addLayout(button_layout)
        
        # Text overlay editor (will be shown when testing)
        self.text_editor = None
        
        # Set dark theme
        self.setStyleSheet("""
            QMainWindow {
                background-color: #1e1e1e;
                color: #ffffff;
            }
            QWidget {
                background-color: #1e1e1e;
                color: #ffffff;
            }
        """)
        
    def log(self, message):
        """Add message to log"""
        print(message)
        self.log_text.append(message)
        QApplication.processEvents()
        
    def test_font_manager(self):
        """Test the font manager"""
        self.log("🔤 Testing Font Manager...")
        
        try:
            font_manager = get_font_manager()
            
            # Test English fonts
            english_fonts = font_manager.get_english_fonts()
            self.log(f"✅ Found {len(english_fonts)} English fonts:")
            for i, font_name in enumerate(list(english_fonts.keys())[:5]):
                self.log(f"   {i+1}. {font_name}")
            if len(english_fonts) > 5:
                self.log(f"   ... and {len(english_fonts) - 5} more")
            
            # Test Urdu fonts
            urdu_fonts = font_manager.get_urdu_fonts()
            self.log(f"✅ Found {len(urdu_fonts)} Urdu fonts:")
            for i, font_name in enumerate(list(urdu_fonts.keys())[:5]):
                self.log(f"   {i+1}. {font_name}")
            if len(urdu_fonts) > 5:
                self.log(f"   ... and {len(urdu_fonts) - 5} more")
            
            # Test font creation
            if english_fonts:
                first_font = list(english_fonts.keys())[0]
                test_font = font_manager.get_font(first_font, 16)
                self.log(f"✅ Successfully created QFont for: {first_font}")
            
            self.log("🎉 Font Manager test completed successfully!")
            
        except Exception as e:
            self.log(f"❌ Font Manager test failed: {e}")
    
    def test_text_editor(self):
        """Test the text overlay editor"""
        self.log("📝 Testing Text Overlay Editor...")
        
        try:
            if self.text_editor is None:
                self.text_editor = TextOverlayEditor()
                self.text_editor.overlay_changed.connect(self.on_overlay_changed)
            
            self.text_editor.show()
            self.text_editor.raise_()
            self.text_editor.activateWindow()
            
            self.log("✅ Text Overlay Editor opened successfully!")
            self.log("📝 Try changing fonts and text to test the system")
            
        except Exception as e:
            self.log(f"❌ Text Editor test failed: {e}")
    
    def test_urdu_fonts(self):
        """Test Urdu fonts specifically"""
        self.log("🕌 Testing Urdu Fonts...")
        
        try:
            font_manager = get_font_manager()
            urdu_fonts = font_manager.get_urdu_fonts()
            
            if not urdu_fonts:
                self.log("⚠️ No Urdu fonts found. Check if Urdu font files are in the 'Urdu Fonts' folder.")
                return
            
            # Test creating Urdu fonts
            urdu_test_text = "یہ اردو فونٹ کا نمونہ ہے - All is Greatest"
            
            for i, font_name in enumerate(list(urdu_fonts.keys())[:3]):
                try:
                    font = font_manager.get_font(font_name, 18)
                    self.log(f"✅ Urdu font {i+1}: {font_name} - Created successfully")
                except Exception as e:
                    self.log(f"❌ Urdu font {i+1}: {font_name} - Error: {e}")
            
            self.log(f"🕌 Urdu font test completed! Test text: {urdu_test_text}")
            
        except Exception as e:
            self.log(f"❌ Urdu font test failed: {e}")
    
    def launch_premiere_interface(self):
        """Launch the premiere interface with text overlay support"""
        self.log("🎬 Launching Premiere Interface...")
        
        try:
            self.premiere = PremiereInterface()
            self.premiere.show()
            self.premiere.raise_()
            self.premiere.activateWindow()
            
            self.log("✅ Premiere Interface launched successfully!")
            self.log("📝 Go to the 'Text' tab to add text overlays to videos")
            self.log("🎬 Load a video first, then add text overlays")
            
        except Exception as e:
            self.log(f"❌ Premiere Interface launch failed: {e}")
    
    def on_overlay_changed(self, overlay):
        """Handle overlay changes from text editor"""
        self.log(f"📝 Text overlay updated:")
        self.log(f"   Text: '{overlay.text}'")
        self.log(f"   Font: {overlay.font_family}")
        self.log(f"   Size: {overlay.font_size}px")
        self.log(f"   Position: {overlay.position_x}%, {overlay.position_y}%")

def main():
    app = QApplication(sys.argv)
    
    # Set application style
    app.setStyle('Fusion')
    
    # Create and show test window
    window = FontTestWindow()
    window.show()
    
    # Auto-test font manager after 2 seconds
    QTimer.singleShot(2000, window.test_font_manager)
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
