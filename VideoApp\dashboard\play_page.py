# -*- coding: utf-8 -*-
from core.libraries import *
from ui.video_player_widget import VideoPlayerWidget
from ui.playback_controls import PlaybackControls
from ui.audio_mixer import AudioMixerGroupBox
from ui.speed_control import SpeedControl
from ui.bookmark_manager import BookmarkManager

class PlayPage(QWidget):
    def __init__(self, parent=None, video_player=None):
        super().__init__(parent)
        self.video_player = video_player

        # Create main splitter for resizable panels
        self.main_splitter = QSplitter(Qt.Horizontal)
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(5, 5, 5, 5)
        main_layout.addWidget(self.main_splitter)

        # Left panel for controls and information with improved sizing
        left_panel = QWidget()
        left_panel.setMinimumWidth(320)
        left_panel.setMaximumWidth(480)
        left_layout = QVBoxLayout(left_panel)
        left_layout.setContents<PERSON>argins(12, 12, 12, 12)
        left_layout.setSpacing(8)

        # Style the left panel
        left_panel.setStyleSheet("""
            QWidget {
                background-color: #2b2b2b;
                border-radius: 8px;
            }
            QLabel {
                color: #ffffff;
                font-weight: bold;
                font-size: 13px;
                padding: 5px;
            }
            QPushButton {
                background-color: #404040;
                color: #ffffff;
                border: 1px solid #555555;
                border-radius: 6px;
                padding: 10px 15px;
                font-size: 12px;
                min-height: 25px;
            }
            QPushButton:hover {
                background-color: #505050;
                border-color: #777777;
            }
            QPushButton:pressed {
                background-color: #353535;
            }
            QLineEdit {
                background-color: #404040;
                color: #ffffff;
                border: 1px solid #555555;
                border-radius: 4px;
                padding: 8px;
                font-size: 12px;
            }
            QLineEdit:focus {
                border-color: #0078d4;
            }
        """)

        # Right panel for video player
        right_panel = QWidget()
        right_layout = QVBoxLayout(right_panel)
        right_layout.setContentsMargins(10, 10, 10, 10)

        # Style the right panel
        right_panel.setStyleSheet("""
            QWidget {
                background-color: #1e1e1e;
                border-radius: 8px;
            }
        """)
        
        # File input section
        file_widget = QWidget()
        file_layout = QVBoxLayout(file_widget)
        
        self.path = QLineEdit()
        self.path.setPlaceholderText("Open local file, stream URL, or paste video link...")
        
        btn_row = QHBoxLayout()
        self.btn_browse = QPushButton("Browse File")
        self.btn_load = QPushButton("Load")
        self.btn_download = QPushButton("Download from URL")
        
        btn_row.addWidget(self.btn_browse)
        btn_row.addWidget(self.btn_load)
        btn_row.addWidget(self.btn_download)
        
        file_layout.addWidget(QLabel("Media Source"))
        file_layout.addWidget(self.path)
        file_layout.addLayout(btn_row)
        
        # Video information panel
        info_widget = QGroupBox("Video Information")
        info_layout = QVBoxLayout(info_widget)
        
        self.info_label = QLabel("No video loaded")
        self.info_label.setWordWrap(True)
        info_layout.addWidget(self.info_label)
        
        # Playback controls
        self.playback_controls = PlaybackControls()
        
        # Audio mixer
        self.audio_mixer = AudioMixerGroupBox()
        
        # Speed control
        self.speed_control = SpeedControl()
        
        # Bookmark manager
        self.bookmark_manager = BookmarkManager()
        
        # Add to left layout
        left_layout.addWidget(file_widget)
        left_layout.addWidget(info_widget)
        left_layout.addWidget(self.playback_controls)
        left_layout.addWidget(self.audio_mixer)
        left_layout.addWidget(self.speed_control)
        left_layout.addWidget(self.bookmark_manager)
        
        # Video player
        self.player = VideoPlayerWidget(self)
        
        # Add player to right layout
        right_layout.addWidget(QLabel("Player"))
        right_layout.addWidget(self.player, 1)
        
        # Add panels to splitter for resizable layout
        self.main_splitter.addWidget(left_panel)
        self.main_splitter.addWidget(right_panel)

        # Set splitter proportions with better balance to prevent overlapping
        self.main_splitter.setSizes([380, 820])
        self.main_splitter.setStretchFactor(0, 0)  # Left panel fixed
        self.main_splitter.setStretchFactor(1, 1)  # Right panel stretches

        # Set minimum sizes to prevent overlapping
        self.main_splitter.setChildrenCollapsible(False)
        
        # Connect signals
        self.btn_browse.clicked.connect(self.on_browse)
        self.btn_load.clicked.connect(self.on_load)
        self.btn_download.clicked.connect(self.on_download)
        self.path.returnPressed.connect(self.on_load)
        
        # Connect playback controls
        self.playback_controls.play_clicked.connect(self.player.play)
        self.playback_controls.pause_clicked.connect(self.player.pause)
        self.playback_controls.stop_clicked.connect(self.player.stop)
        self.playback_controls.seek_requested.connect(self.seek_to_position)
        self.playback_controls.volume_changed.connect(self.set_volume)

        # Connect audio mixer
        self.audio_mixer.track_added.connect(self.add_audio_track)
        self.audio_mixer.track_removed.connect(self.remove_audio_track)
        self.audio_mixer.track_volume_changed.connect(self.adjust_audio_track_volume)

        # Connect speed control
        self.speed_control.speed_changed.connect(self.set_playback_speed)
        
        # Connect bookmark manager
        self.bookmark_manager.bookmark_selected.connect(self.go_to_bookmark)
        
        # Connect player signals
        self.player.time_changed.connect(self.update_position)
        self.player.duration_loaded.connect(self.update_duration)
        self.player.playback_started.connect(self.on_playback_started)
        self.player.error_occurred.connect(self.on_player_error)
        
        self.current_file = None
        self.audio_tracks = {}

    def on_browse(self):
        f, _ = QFileDialog.getOpenFileName(
            self, 
            "Open media file", 
            "", 
            "Media Files (*.mp4 *.mkv *.webm *.mov *.avi *.mp3 *.wav *.aac);;All Files (*.*)"
        )
        if f:
            self.path.setText(f)
            self.load_file(f)

    def on_load(self):
        url = self.path.text().strip()
        if url:
            self.load_file(url)

    def on_download(self):
        """Download video from URL"""
        url = self.path.text().strip()
        if not url:
            QMessageBox.warning(self, "URL Required", "Please enter a video URL to download.")
            return

        # Simple download functionality
        try:
            from utils.video_downloader import download_video_standalone

            # Show progress message
            QMessageBox.information(self, "Download", "Download started. This may take a while...")

            # Download the video
            downloaded_path = download_video_standalone(url)
            if downloaded_path and os.path.exists(downloaded_path):
                self.path.setText(downloaded_path)
                self.load_file(downloaded_path)
                QMessageBox.information(self, "Success", f"Video downloaded successfully: {downloaded_path}")
            else:
                QMessageBox.warning(self, "Error", "Download failed or file not found")

        except Exception as e:
            QMessageBox.warning(self, "Download Error", f"Failed to download video: {str(e)}")

    def load_file(self, path):
        """Load a media file with proper error handling"""
        try:
            print(f"Loading file: {path}")

            # Check if file exists for local files
            if not path.startswith(('http://', 'https://')) and not os.path.exists(path):
                raise FileNotFoundError(f"File not found: {path}")

            self.player.load(path)
            self.current_file = path

            # Don't auto-play, let user control playback
            # self.player.play()

            # Update UI elements
            self.update_video_info()

            print(f"Successfully loaded: {path}")

        except Exception as e:
            print(f"Load error: {e}")
            QMessageBox.warning(self, "Load Error", f"Failed to load media: {str(e)}")

    def update_video_info(self):
        """Update video information display"""
        if self.current_file:
            try:
                # Basic file information
                if os.path.exists(self.current_file):
                    file_size = os.path.getsize(self.current_file)
                    file_size_mb = file_size / (1024 * 1024)
                    filename = os.path.basename(self.current_file)
                else:
                    file_size_mb = 0
                    filename = self.current_file

                info_text = f"""
                <b>File:</b> {filename}<br>
                <b>Size:</b> {file_size_mb:.1f} MB<br>
                <b>Path:</b> {self.current_file}<br>
                <b>Status:</b> Loaded successfully
                """
                self.info_label.setText(info_text)
            except Exception as e:
                print(f"Error updating video info: {e}")
                self.info_label.setText(f"<b>File:</b> {os.path.basename(self.current_file)}<br><b>Status:</b> Loaded")
        else:
            self.info_label.setText("No video loaded")

    def update_position(self, position):
        """Update UI with current playback position"""
        self.playback_controls.set_position(position)
        self.bookmark_manager.update_position(position)

    def update_duration(self, duration):
        """Update UI with media duration"""
        self.playback_controls.set_duration(duration)

    def on_playback_finished(self):
        """Handle playback finished event"""
        self.playback_controls.set_playing(False)

    def on_playback_started(self):
        """Handle playback started event"""
        self.playback_controls.set_playing(True)
        self.update_video_info()

    def on_player_error(self, error_message):
        """Handle player error"""
        QMessageBox.warning(self, "Player Error", f"Playback error: {error_message}")

    def seek_forward(self, seconds):
        """Seek forward by specified seconds"""
        current_pos = self.player.get_position()
        self.player.seek(current_pos + seconds)

    def seek_backward(self, seconds):
        """Seek backward by specified seconds"""
        current_pos = self.player.get_position()
        self.player.seek(max(0, current_pos - seconds))

    def seek_to_position(self, position):
        """Seek to specific position in seconds"""
        self.player.seek(position)

    def set_volume(self, volume):
        """Set main volume"""
        self.player.set_volume(volume)

    def set_playback_speed(self, speed):
        """Set playback speed"""
        self.player.set_speed(speed)

    def add_audio_track(self, audio_path):
        """Add additional audio track"""
        if audio_path not in self.audio_tracks:
            track_id = self.player.add_audio_track(audio_path)
            if track_id:
                self.audio_tracks[audio_path] = track_id
                return True
        return False

    def remove_audio_track(self, audio_path):
        """Remove audio track"""
        if audio_path in self.audio_tracks:
            self.player.remove_audio_track(self.audio_tracks[audio_path])
            del self.audio_tracks[audio_path]

    def adjust_audio_track_volume(self, audio_path, volume):
        """Adjust volume of specific audio track"""
        if audio_path in self.audio_tracks:
            self.player.set_audio_track_volume(self.audio_tracks[audio_path], volume)

    def add_bookmark(self, name, position):
        """Add a bookmark at current position"""
        self.bookmark_manager.add_bookmark(name, position)

    def go_to_bookmark(self, position):
        """Seek to bookmark position"""
        self.player.seek(position)

    def keyPressEvent(self, event):
        """Handle keyboard shortcuts"""
        if event.key() == Qt.Key_Space:
            if self.player.is_playing():
                self.player.pause()
            else:
                self.player.play()
        elif event.key() == Qt.Key_Right:
            self.seek_forward(5)
        elif event.key() == Qt.Key_Left:
            self.seek_backward(5)
        elif event.key() == Qt.Key_Up:
            self.player.set_volume(min(100, self.player.get_volume() + 10))
        elif event.key() == Qt.Key_Down:
            self.player.set_volume(max(0, self.player.get_volume() - 10))
        elif event.key() == Qt.Key_F:
            self.player.toggle_fullscreen()
        elif event.key() == Qt.Key_B:
            # Add bookmark with timestamp
            pos = self.player.get_position()
            self.add_bookmark(f"Bookmark at {format_time(pos)}", pos)
        else:
            super().keyPressEvent(event)

    def closeEvent(self, event):
        """Clean up resources when closing"""
        self.player.cleanup()
        event.accept()

def format_time(seconds):
    """Format seconds to HH:MM:SS"""
    hours = int(seconds // 3600)
    minutes = int((seconds % 3600) // 60)
    seconds = int(seconds % 60)
    return f"{hours:02d}:{minutes:02d}:{seconds:02d}"
