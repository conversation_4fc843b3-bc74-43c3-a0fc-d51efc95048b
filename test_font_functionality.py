#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Comprehensive test for font functionality
"""

import os
import sys

def test_font_directories():
    """Test if font directories exist"""
    print("🔍 Testing Font Directories...")
    
    english_fonts_dir = "English Fonts"
    urdu_fonts_dir = "Urdu Fonts"
    
    # Check English fonts
    if os.path.exists(english_fonts_dir):
        english_files = os.listdir(english_fonts_dir)
        print(f"✅ English Fonts directory found with {len(english_files)} files:")
        for i, file in enumerate(english_files[:5]):
            print(f"   {i+1}. {file}")
        if len(english_files) > 5:
            print(f"   ... and {len(english_files) - 5} more files")
    else:
        print(f"❌ English Fonts directory not found at: {os.path.abspath(english_fonts_dir)}")
    
    # Check Urdu fonts
    if os.path.exists(urdu_fonts_dir):
        urdu_files = os.listdir(urdu_fonts_dir)
        print(f"✅ Urdu Fonts directory found with {len(urdu_files)} files:")
        for i, file in enumerate(urdu_files[:5]):
            print(f"   {i+1}. {file}")
        if len(urdu_files) > 5:
            print(f"   ... and {len(urdu_files) - 5} more files")
    else:
        print(f"❌ Urdu Fonts directory not found at: {os.path.abspath(urdu_fonts_dir)}")
    
    print()

def test_font_manager():
    """Test font manager functionality"""
    print("🔤 Testing Font Manager...")
    
    try:
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'VideoApp'))
        from utils.font_manager import FontManager
        
        # Create font manager
        font_manager = FontManager()
        font_manager.initialize_fonts()
        
        # Test English fonts
        english_fonts = font_manager.get_english_fonts()
        print(f"✅ Font Manager loaded {len(english_fonts)} English fonts")
        
        # Test Urdu fonts
        urdu_fonts = font_manager.get_urdu_fonts()
        print(f"✅ Font Manager loaded {len(urdu_fonts)} Urdu fonts")
        
        # Test font creation
        if english_fonts:
            first_font = list(english_fonts.keys())[0]
            font = font_manager.get_font(first_font, 16)
            print(f"✅ Successfully created font: {first_font}")
        
        print("✅ Font Manager test passed!")
        
    except Exception as e:
        print(f"❌ Font Manager test failed: {e}")
        import traceback
        traceback.print_exc()
    
    print()

def test_text_overlay():
    """Test text overlay functionality"""
    print("📝 Testing Text Overlay...")
    
    try:
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'VideoApp'))
        from ui.text_overlay_editor import TextOverlay
        
        # Create text overlay
        overlay = TextOverlay()
        overlay.text = "All is Greatest - یہ بہترین ہے"
        overlay.font_family = "Arial"
        overlay.font_size = 24
        
        print(f"✅ Text Overlay created successfully:")
        print(f"   Text: {overlay.text}")
        print(f"   Font: {overlay.font_family}")
        print(f"   Size: {overlay.font_size}px")
        print(f"   Position: {overlay.position_x}%, {overlay.position_y}%")
        print(f"   Duration: {overlay.start_time}s - {overlay.end_time}s")
        
        print("✅ Text Overlay test passed!")
        
    except Exception as e:
        print(f"❌ Text Overlay test failed: {e}")
        import traceback
        traceback.print_exc()
    
    print()

def test_text_renderer():
    """Test text renderer functionality"""
    print("🎨 Testing Text Renderer...")
    
    try:
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'VideoApp'))
        from utils.text_renderer import TextRenderer
        from ui.text_overlay_editor import TextOverlay
        import numpy as np
        
        # Create text renderer
        renderer = TextRenderer()
        
        # Create test frame (dummy video frame)
        test_frame = np.zeros((480, 640, 3), dtype=np.uint8)
        test_frame[:] = (50, 50, 50)  # Dark gray background
        
        # Create text overlay
        overlay = TextOverlay()
        overlay.text = "Test Text - All is Greatest"
        overlay.font_family = "Arial"
        overlay.font_size = 32
        overlay.position_x = 50
        overlay.position_y = 50
        overlay.start_time = 0.0
        overlay.end_time = 10.0
        
        # Test rendering
        frame_with_text = renderer.render_text_on_frame(test_frame, overlay, 5.0)
        
        print(f"✅ Text Renderer test passed!")
        print(f"   Original frame shape: {test_frame.shape}")
        print(f"   Frame with text shape: {frame_with_text.shape}")
        print(f"   Text rendered at time 5.0s")
        
    except Exception as e:
        print(f"❌ Text Renderer test failed: {e}")
        import traceback
        traceback.print_exc()
    
    print()

def test_dependencies():
    """Test required dependencies"""
    print("📦 Testing Dependencies...")
    
    dependencies = [
        ("PyQt5", "PyQt5"),
        ("OpenCV", "cv2"),
        ("Pillow", "PIL"),
        ("NumPy", "numpy")
    ]
    
    for name, module in dependencies:
        try:
            __import__(module)
            print(f"✅ {name} is available")
        except ImportError:
            print(f"❌ {name} is missing - install with: pip install {module}")
    
    print()

def main():
    print("🎨 Font System Comprehensive Test")
    print("=" * 50)
    print()
    
    # Test all components
    test_dependencies()
    test_font_directories()
    test_font_manager()
    test_text_overlay()
    test_text_renderer()
    
    print("🎉 Font System Test Complete!")
    print()
    print("📝 To use the font system:")
    print("1. Run: python VideoApp/main.py gui --style=premiere")
    print("2. Click on the '📝 Text' tab in the left panel")
    print("3. Enter your text (English or Urdu)")
    print("4. Choose fonts from the dropdown")
    print("5. Adjust position, size, and timing")
    print("6. Click 'Apply Text' to add to video")
    print("7. Use 'Export with Text' to render final video")
    print()
    print("🕌 For Urdu text, try: 'یہ اردو فونٹ کا نمونہ ہے - All is Greatest'")

if __name__ == "__main__":
    main()
