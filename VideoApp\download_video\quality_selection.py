# -*- coding: utf-8 -*-
# Handles video quality selection

from typing import Dict, List, Union, Any

# Import locally to avoid circular imports
def formats(url: str, cache: bool = True):
    """Local import to avoid circular imports"""
    from download_video.list_formats import formats as list_formats_func
    return list_formats_func(url, cache)

def get_recommended_format(url: str):
    """Local import to avoid circular imports"""
    from download_video.list_formats import get_recommended_format as get_recommended_func
    return get_recommended_func(url)

def list_quality(url: str) -> Dict[str, Union[List[Dict[str, Any]], str]]:
    """
    Retrieve available quality options for a given video URL.
    
    Args:
        url: URL of the video to analyze
        
    Returns:
        Dictionary with 'success' status, 'qualities' list, and optional 'error' message
    """
    result = formats(url)
    
    # Rename the key from 'formats' to 'qualities' for consistency with function name
    if result.get("success", False):
        result["qualities"] = result.pop("formats")
    
    return result

def get_quality_by_id(url: str, format_id: str) -> Dict[str, Any]:
    """
    Get specific quality information by format ID.
    
    Args:
        url: URL of the video to analyze
        format_id: The format ID to look for
        
    Returns:
        Dictionary with quality information
    """
    result = list_quality(url)
    
    if not result["success"]:
        return result
    
    for quality in result["qualities"]:
        if quality.format_id == format_id:
            return {
                "success": True,
                "quality": quality,
                "source_url": url
            }
    
    return {
        "success": False,
        "error": f"Format ID {format_id} not found",
        "source_url": url,
        "available_formats": [q.format_id for q in result["qualities"]]
    }

def get_quality_by_resolution(url: str, resolution: str) -> Dict[str, Any]:
    """
    Get quality information for a specific resolution.
    
    Args:
        url: URL of the video to analyze
        resolution: The resolution to look for (e.g., "1080p", "720p")
        
    Returns:
        Dictionary with quality information
    """
    result = list_quality(url)
    
    if not result["success"]:
        return result
    
    matching_qualities = []
    for quality in result["qualities"]:
        if resolution in quality.resolution:
            matching_qualities.append(quality)
    
    if not matching_qualities:
        return {
            "success": False,
            "error": f"Resolution {resolution} not found",
            "source_url": url,
            "available_resolutions": list(set([q.resolution for q in result["qualities"]]))
        }
    
    # Return the best quality option for this resolution
    best_quality = max(matching_qualities, key=lambda x: x.quality)
    
    return {
        "success": True,
        "qualities": matching_qualities,
        "recommended": best_quality,
        "source_url": url
    }
