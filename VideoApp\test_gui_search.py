#!/usr/bin/env python3
"""
Test GUI search functionality
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
from PyQt5.QtCore import QTimer
from ui.search_interface import VideoSearchWidget

class TestSearchWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Test Video Search")
        self.setGeometry(100, 100, 800, 600)
        
        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Create layout
        layout = QVBoxLayout(central_widget)
        
        # Create search widget
        self.search_widget = VideoSearchWidget()
        layout.addWidget(self.search_widget)
        
        # Connect signals
        self.search_widget.video_selected.connect(self.on_video_selected)
        
        # Auto-test after 2 seconds
        QTimer.singleShot(2000, self.auto_test)
        
    def on_video_selected(self, video_info):
        print(f"Video selected: {video_info.get('title', 'Unknown')}")
        
    def auto_test(self):
        """Automatically test search functionality"""
        print("Starting auto-test...")
        
        # Set search text
        self.search_widget.search_input.setText("python tutorial")
        
        # Trigger search
        self.search_widget.perform_search()
        
        print("Search triggered!")

if __name__ == "__main__":
    app = QApplication(sys.argv)
    
    # Set dark theme
    app.setStyleSheet("""
        QMainWindow {
            background-color: #2b2b2b;
            color: white;
        }
        QWidget {
            background-color: #2b2b2b;
            color: white;
        }
    """)
    
    window = TestSearchWindow()
    window.show()
    
    print("GUI test window opened. Search will auto-start in 2 seconds...")
    
    sys.exit(app.exec_())
