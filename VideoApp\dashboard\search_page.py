# -*- coding: utf-8 -*-
from core.libraries import *
from core.theme import *
from utils.search_engine import MultiPlatformSearch
from ui.video_player_widget import VideoPlayerWidget
from ui.download_button import DownloadButton
import threading
from download_video.video_download import download_video
from ui.progress_dialog import ProgressDialog
from PyQt5.QtCore import QTimer, pyqtSignal


class SearchPage(QWidget):
    """Search bar with suggestions, multi-platform results, and inline video player."""
    
    # Define signals at class level
    download_complete_signal = pyqtSignal(str)
    download_error_signal = pyqtSignal(str)
    search_results_ready = pyqtSignal(list)
    search_error = pyqtSignal(str)
    
    def __init__(self, parent=None, video_player=None):
        super().__init__(parent)
        self.video_player = video_player
        self.engine = MultiPlatformSearch()
        self.current_result = None

        # Create main splitter for resizable panels
        self.main_splitter = QSplitter(Qt.Horizontal)
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(5, 5, 5, 5)
        main_layout.addWidget(self.main_splitter)

        # Left panel for search and results with elegant styling
        left_panel = QWidget()
        left_panel.setMinimumWidth(380)
        left_panel.setMaximumWidth(520)
        left_layout = QVBoxLayout(left_panel)
        left_layout.setContentsMargins(12, 12, 12, 12)
        left_layout.setSpacing(8)

        # Apply consistent VS Code theme styling
        from core.theme import DARK_THEME
        left_panel.setStyleSheet(f"""
            QWidget {{
                background-color: {DARK_THEME['PANEL_COLOR']};
                border-radius: 8px;
            }}
            QLabel {{
                color: {DARK_THEME['TEXT_COLOR']};
                font-weight: 500;
                font-size: {DARK_THEME['APP_FONT_SIZE']}pt;
                padding: 5px;
            }}
            QPushButton {{
                background-color: {DARK_THEME['PANEL_COLOR']};
                color: {DARK_THEME['TEXT_COLOR']};
                border: 1px solid {DARK_THEME['BORDER_COLOR']};
                border-radius: 4px;
                padding: 12px 18px;
                font-size: {DARK_THEME['APP_FONT_SIZE']}pt;
                font-weight: 500;
                min-height: 30px;
            }}
            QPushButton:hover {{
                background-color: {DARK_THEME['ACCENT_COLOR']};
                color: #ffffff;
            }}
            QPushButton:pressed {{
                background-color: {DARK_THEME['ACCENT_COLOR_DARK']};
            }}
            QLineEdit {{
                background-color: {DARK_THEME['PANEL_COLOR']};
                color: {DARK_THEME['TEXT_COLOR']};
                border: 1px solid {DARK_THEME['BORDER_COLOR']};
                border-radius: 4px;
                padding: 12px;
                font-size: {DARK_THEME['APP_FONT_SIZE']}pt;
                min-height: 25px;
            }}
            QLineEdit:focus {{
                border-color: {DARK_THEME['ACCENT_COLOR']};
                background-color: {DARK_THEME['PRIMARY_COLOR']};
            }}
            QComboBox {{
                background-color: {DARK_THEME['PANEL_COLOR']};
                color: {DARK_THEME['TEXT_COLOR']};
                border: 1px solid {DARK_THEME['BORDER_COLOR']};
                border-radius: 4px;
                padding: 10px;
                font-size: {DARK_THEME['APP_FONT_SIZE']}pt;
                min-height: 25px;
            }}
            QListWidget {{
                background-color: {DARK_THEME['BACKGROUND_COLOR']};
                color: {DARK_THEME['TEXT_COLOR']};
                border: 1px solid {DARK_THEME['BORDER_COLOR']};
                border-radius: 4px;
                padding: 5px;
                font-size: {DARK_THEME['APP_FONT_SIZE']}pt;
            }}
            QListWidget::item {{
                padding: 12px;
                border-bottom: 1px solid {DARK_THEME['BORDER_COLOR']};
                border-radius: 4px;
                margin: 2px;
                min-height: 20px;
            }}
            QListWidget::item:hover {{
                background-color: {DARK_THEME['PRIMARY_COLOR']};
            }}
            QListWidget::item:selected {{
                background-color: {DARK_THEME['ACCENT_COLOR']};
                color: #ffffff;
            }}
        """)

        # Right panel for video player
        right_panel = QWidget()
        right_layout = QVBoxLayout(right_panel)
        right_layout.setContentsMargins(10, 10, 10, 10)

        # Style the right panel with consistent theme
        right_panel.setStyleSheet(f"""
            QWidget {{
                background-color: {DARK_THEME['BACKGROUND_COLOR']};
                border-radius: 8px;
            }}
        """)
        
        # 🔍 Search bar
        search_widget = QWidget()
        search_layout = QHBoxLayout(search_widget)
        search_layout.setContentsMargins(0, 0, 0, 0)
        self.query = QLineEdit()
        self.query.setPlaceholderText("Type here to search videos...")
        self.query.returnPressed.connect(self.on_enter)
        self.search_btn = QPushButton("🔍 Search")
        self.search_btn.clicked.connect(self.on_enter)
        
        search_layout.addWidget(self.query)
        search_layout.addWidget(self.search_btn)
        
        # Platform filter
        platform_widget = QWidget()
        platform_layout = QHBoxLayout(platform_widget)
        platform_layout.setContentsMargins(0, 0, 0, 0)
        platform_layout.addWidget(QLabel("Platform:"))
        self.platform_combo = QComboBox()
        self.platform_combo.addItems(["All", "YouTube", "Facebook", "Instagram", "TikTok", "Twitter"])
        platform_layout.addWidget(self.platform_combo)
        platform_layout.addStretch()
        
        # 📋 Suggestions
        self.suggestions = QListWidget()
        self.suggestions.setStyleSheet(
            f"""
            QListWidget {{
                background-color: {DARK_THEME['PANEL_COLOR']};
                color: {DARK_THEME['TEXT_COLOR']};
                border: 1px solid {DARK_THEME['BORDER_COLOR']};
                border-radius: 4px;
            }}
            QListWidget::item {{
                padding: 8px;
                border-bottom: 1px solid {DARK_THEME['BORDER_COLOR']};
            }}
            QListWidget::item:selected {{
                background-color: {DARK_THEME['ACCENT_COLOR']};
                color: #ffffff;
            }}
            """
        )
        self.suggestions.itemClicked.connect(self.on_pick_suggestion)
        self.suggestions.setVisible(False)  # Initially hidden

        # 📋 Results
        self.results = QListWidget()
        self.results.setStyleSheet(
            f"""
            QListWidget {{
                background-color: {DARK_THEME['PANEL_COLOR']};
                color: {DARK_THEME['TEXT_COLOR']};
                border: 1px solid {DARK_THEME['BORDER_COLOR']};
                border-radius: 4px;
            }}
            QListWidget::item {{
                padding: 12px;
                border-bottom: 1px solid {DARK_THEME['BORDER_COLOR']};
            }}
            QListWidget::item:selected {{
                background-color: {DARK_THEME['ACCENT_COLOR']};
                color: #ffffff;
            }}
            QListWidget::item:hover {{
                background-color: {DARK_THEME['PRIMARY_COLOR']};
            }}
            """
        )
        self.results.itemClicked.connect(self.on_pick_result)

        # 🎬 Video Player
        self.player = VideoPlayerWidget(self)
        
        # Action buttons for selected result
        action_widget = QWidget()
        action_layout = QHBoxLayout(action_widget)
        action_layout.setContentsMargins(0, 0, 0, 0)
        self.download_btn = DownloadButton()
        self.edit_btn = QPushButton("Edit Video")
        self.edit_btn.setEnabled(False)
        
        action_layout.addWidget(self.download_btn)
        action_layout.addWidget(self.edit_btn)
        
        # Status label
        self.status_label = QLabel("Ready to search")
        self.status_label.setStyleSheet("""
            QLabel {
                color: #888888;
                font-size: 11px;
                padding: 5px;
                background-color: #2d2d30;
                border-radius: 4px;
            }
        """)
        
        # Add to left layout
        left_layout.addWidget(QLabel("🔍 VIDEO SEARCH"))
        left_layout.addWidget(search_widget)
        left_layout.addWidget(platform_widget)
        left_layout.addWidget(QLabel("Suggestions"))
        left_layout.addWidget(self.suggestions, 1)
        left_layout.addWidget(QLabel("Results"))
        left_layout.addWidget(self.results, 2)
        left_layout.addWidget(action_widget)
        left_layout.addWidget(self.status_label)
        
        # Add to right layout
        right_layout.addWidget(QLabel("Preview"))
        right_layout.addWidget(self.player, 1)
        
        # Add panels to splitter for resizable layout
        self.main_splitter.addWidget(left_panel)
        self.main_splitter.addWidget(right_panel)

        # Set splitter proportions with better balance to prevent overlapping
        self.main_splitter.setSizes([420, 680])
        self.main_splitter.setStretchFactor(0, 0)  # Left panel fixed size
        self.main_splitter.setStretchFactor(1, 1)  # Right panel stretches
        
        # Connect signals
        self.query.textChanged.connect(self.on_type)
        self.download_btn.download_btn.clicked.connect(self.on_download)
        self.download_btn.download_progress.connect(self.handle_download_progress)
        self.download_btn.download_finished.connect(self.handle_download_complete)
        self.download_btn.download_error.connect(self.handle_download_error)
        self.edit_btn.clicked.connect(self.on_edit)
        
        # Connect search signals
        self.search_results_ready.connect(self._display_results)
        self.search_error.connect(self._display_error)
        
        self.current_result = None

    # --- Events ---
    def on_type(self, text):
        """Show autocomplete suggestions while typing"""
        if len(text) > 2:  # Only show suggestions after 3 characters
            self.suggestions.clear()
            self.suggestions.setVisible(True)
            suggestions = self.engine.autocomplete(text)[:5]  # Limit to 5 suggestions
            for s in suggestions:
                self.suggestions.addItem(s)
            if not suggestions:
                self.suggestions.setVisible(False)
        else:
            self.suggestions.setVisible(False)

    def on_enter(self):
        """Trigger search when pressing Enter or clicking search button"""
        text = self.query.text().strip()
        platform = self.platform_combo.currentText()
        if text:
            self.populate_results(text, platform)
        else:
            self.status_label.setText("Please enter search terms")

    def on_pick_suggestion(self, item):
        """When a suggestion is clicked, trigger search"""
        try:
            if item and item.text():
                self.query.setText(item.text())
                self.suggestions.setVisible(False)
                self.populate_results(item.text(), self.platform_combo.currentText())
        except Exception as e:
            print(f"Error in suggestion selection: {e}")
            QMessageBox.warning(self, "Error", f"Failed to select suggestion: {str(e)}")

    def populate_results(self, text, platform="All"):
        """Populate results list with formatted items from all platforms"""
        self.results.clear()
        self.status_label.setText(f"Searching for '{text}'...")
        
        try:
            # Show loading indicator
            loading_item = QListWidgetItem("🔍 Searching... Please wait")
            loading_item.setFlags(Qt.NoItemFlags)  # Make it non-selectable
            self.results.addItem(loading_item)
            
            # Force UI update
            QApplication.processEvents()
            
            # Filter by platform if not "All"
            filters = {}
            if platform != "All":
                filters['platform'] = platform.lower()

            # Run search in thread to avoid UI freezing
            def search_thread():
                try:
                    results = self.engine.search(text, max_results=15, filters=filters)
                    if not results:
                        self.search_error.emit("No results found. Try different search terms or check your internet connection.")
                    else:
                        self.search_results_ready.emit(results)
                except Exception as e:
                    self.search_error.emit(f"Search error: {str(e)}")
            
            threading.Thread(target=search_thread, daemon=True).start()

        except Exception as e:
            self._display_error(str(e))

    def _display_results(self, results):
        """Display search results"""
        self.results.clear()
        
        if not results:
            no_results_item = QListWidgetItem("❌ No videos found. Try different search terms")
            no_results_item.setFlags(Qt.NoItemFlags)
            self.results.addItem(no_results_item)
            self.status_label.setText("No results found")
            return
        
        for res in results:
            title = res.get("title", "Untitled")
            views = res.get("views", "Unknown views")
            platform_name = res.get("platform", "Unknown")
            duration = res.get("duration", "Unknown")

            item_text = f"🎬 {title}\n📺 Platform: {platform_name} | 👁️ Views: {views} | ⏱️ Duration: {duration}"
            item = QListWidgetItem(item_text)

            result_data = {
                "title": title,
                "url": res.get("url", ""),
                "stream_url": res.get("stream_url", ""),
                "platform": platform_name,
                "views": views,
                "duration": duration,
                "thumbnail": res.get("thumbnail", ""),
                "description": res.get("description", "")
            }

            item.setData(Qt.UserRole, result_data)
            self.results.addItem(item)
        
        self.status_label.setText(f"✅ Found {len(results)} videos")
        self.edit_btn.setEnabled(False)
        self.download_btn.setEnabled(False)

    def _display_error(self, error_msg):
        """Display error message"""
        self.results.clear()
        error_item = QListWidgetItem(f"❌ {error_msg}")
        error_item.setFlags(Qt.NoItemFlags)
        self.results.addItem(error_item)
        
        help_item = QListWidgetItem("💡 Try checking your internet connection or using different search terms")
        help_item.setFlags(Qt.NoItemFlags)
        self.results.addItem(help_item)
        
        self.status_label.setText(f"Error: {error_msg}")
        self.edit_btn.setEnabled(False)
        self.download_btn.setEnabled(False)

    def on_pick_result(self, item):
        """When a result is clicked, play it in the video player"""
        try:
            if not item:
                return

            self.current_result = item.data(Qt.UserRole)
            print(f"Selected result: {self.current_result}")

            if not self.current_result:
                QMessageBox.information(self, "Info", "No video data available for this item")
                return

            # Get URLs
            stream_url = self.current_result.get("stream_url")
            page_url = self.current_result.get("url")
            url_to_load = stream_url or page_url

            if url_to_load:
                try:
                    # Load the video
                    self.player.load(url_to_load)

                    # Enable action buttons
                    self.download_btn.setEnabled(True)
                    self.edit_btn.setEnabled(True)

                    # Set download URL
                    if hasattr(self.download_btn, 'set_download_url'):
                        self.download_btn.set_download_url(page_url or url_to_load)

                    self.status_label.setText("✅ Video loaded - Click play to start")

                except Exception as load_error:
                    QMessageBox.warning(self, "Load Error", f"Could not load video: {str(load_error)}")
            else:
                QMessageBox.information(self, "Info", "No playable URL found for this video")

        except Exception as e:
            QMessageBox.warning(self, "Playback Error", f"Could not play video: {str(e)}")

    def safe_play_video(self):
        """Safely attempt to play the loaded video"""
        try:
            if self.player and hasattr(self.player, 'player'):
                self.player.player.play()
        except Exception as e:
            print(f"Error starting playback: {e}")
    
    def on_download(self):
        """Handle download button click"""
        if self.current_result:
            url = self.current_result.get("url")
            if url:
                self.download_btn.start_download()
    
    def on_edit(self):
        """Handle edit button click - send to edit page"""
        if self.current_result and hasattr(self.parent(), 'switch_to_edit_page'):
            url = self.current_result.get("url")
            if url:
                progress_dialog = ProgressDialog("Downloading for editing...", self)
                progress_dialog.show()
                
                def download_thread():
                    try:
                        out = download_video(url, progress_hook=progress_dialog.update_progress)
                        self.download_complete_signal.emit(out)
                    except Exception as e:
                        self.download_error_signal.emit(str(e))
                
                threading.Thread(target=download_thread).start()
    
    def handle_download_progress(self, value):
        """Handle download progress updates"""
        pass
    
    def handle_download_complete(self, out):
        """Handle download completion"""
        if hasattr(self.parent(), 'switch_to_edit_page'):
            self.parent().switch_to_edit_page(out)
    
    def handle_download_error(self, error_msg):
        """Handle download error"""
        QMessageBox.warning(self, "Download Error", f"Failed to download: {error_msg}")
