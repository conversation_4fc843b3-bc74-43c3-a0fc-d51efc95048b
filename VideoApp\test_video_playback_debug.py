#!/usr/bin/env python3
"""
Debug video playback functionality
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QLabel, QFileDialog
from PyQt5.QtCore import QTimer
from PyQt5.QtMultimedia import QMediaPlayer, QMediaContent
from PyQt5.QtCore import QUrl

def test_qt_multimedia():
    """Test Qt multimedia capabilities"""
    print("🧪 Testing Qt Multimedia...")
    
    try:
        from PyQt5.QtMultimedia import QMediaPlayer, QMediaContent, QVideoWidget
        from PyQt5.QtMultimediaWidgets import QVideoWidget
        print("✅ Qt Multimedia imports successful")
        
        # Test creating media player
        player = QMediaPlayer()
        print("✅ QMediaPlayer created successfully")
        
        # Test creating video widget
        video_widget = QVideoWidget()
        print("✅ QVideoWidget created successfully")
        
        # Test setting video output
        player.setVideoOutput(video_widget)
        print("✅ Video output set successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ Qt Multimedia test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_video_player_widget():
    """Test VideoPlayerWidget"""
    print("\n🧪 Testing VideoPlayerWidget...")

    try:
        # Create QApplication if it doesn't exist
        app = QApplication.instance()
        if app is None:
            app = QApplication([])

        from ui.video_player_widget import VideoPlayerWidget
        print("✅ VideoPlayerWidget imported successfully")

        # Create widget
        player_widget = VideoPlayerWidget()
        print("✅ VideoPlayerWidget created successfully")
        
        # Check if player is initialized
        if hasattr(player_widget, 'player') and player_widget.player:
            print("✅ Internal QMediaPlayer initialized")
        else:
            print("❌ Internal QMediaPlayer not initialized")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ VideoPlayerWidget test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_premiere_interface():
    """Test PremiereInterface"""
    print("\n🧪 Testing PremiereInterface...")

    try:
        # Create QApplication if it doesn't exist
        app = QApplication.instance()
        if app is None:
            app = QApplication([])

        from ui.premiere_interface import PremiereInterface
        print("✅ PremiereInterface imported successfully")

        # Create interface (this might take a moment)
        interface = PremiereInterface()
        print("✅ PremiereInterface created successfully")
        
        # Check if video player exists
        if hasattr(interface, 'video_player') and interface.video_player:
            print("✅ Video player component exists")
        else:
            print("❌ Video player component missing")
            return False
        
        # Check if status bar exists
        if hasattr(interface, 'status_bar') and interface.status_bar:
            print("✅ Status bar exists")
        else:
            print("❌ Status bar missing")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ PremiereInterface test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_file_loading():
    """Test file loading with a sample file"""
    print("\n🧪 Testing file loading...")

    # Create QApplication if it doesn't exist
    app = QApplication.instance()
    if app is None:
        app = QApplication([])

    # Look for test video files
    test_paths = [
        "test_videos",
        "../test_videos",
        "downloads",
        "download_video"
    ]
    
    test_file = None
    for path in test_paths:
        if os.path.exists(path):
            for file in os.listdir(path):
                if file.lower().endswith(('.mp4', '.avi', '.mov', '.mkv')):
                    test_file = os.path.join(path, file)
                    break
            if test_file:
                break
    
    if not test_file:
        print("⚠️ No test video file found. Skipping file loading test.")
        print("   Create a 'test_videos' folder with sample video files to test.")
        return True
    
    print(f"📁 Found test file: {test_file}")
    
    try:
        from ui.video_player_widget import VideoPlayerWidget
        
        # Create player widget
        player_widget = VideoPlayerWidget()
        
        # Test loading the file
        print(f"🎬 Testing load: {test_file}")
        player_widget.load(test_file)
        
        print("✅ File loading test completed (check console for any errors)")
        return True
        
    except Exception as e:
        print(f"❌ File loading test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

class VideoTestWindow(QMainWindow):
    """Simple test window for video playback"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Video Playback Test")
        self.setGeometry(100, 100, 800, 600)
        
        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # Status label
        self.status_label = QLabel("Ready to test video playback")
        layout.addWidget(self.status_label)
        
        # Test buttons
        btn_test_player = QPushButton("Test Video Player Widget")
        btn_test_player.clicked.connect(self.test_player_widget)
        layout.addWidget(btn_test_player)
        
        btn_test_file = QPushButton("Test Load Video File")
        btn_test_file.clicked.connect(self.test_load_file)
        layout.addWidget(btn_test_file)
        
        btn_test_premiere = QPushButton("Test Premiere Interface")
        btn_test_premiere.clicked.connect(self.test_premiere)
        layout.addWidget(btn_test_premiere)
        
        # Video player widget for testing
        self.video_player = None
        
    def test_player_widget(self):
        """Test creating video player widget"""
        try:
            self.status_label.setText("Creating VideoPlayerWidget...")
            
            from ui.video_player_widget import VideoPlayerWidget
            self.video_player = VideoPlayerWidget()
            
            # Add to layout
            self.layout().addWidget(self.video_player)
            
            self.status_label.setText("✅ VideoPlayerWidget created successfully!")
            
        except Exception as e:
            self.status_label.setText(f"❌ Error: {str(e)}")
            print(f"Error creating video player: {e}")
            import traceback
            traceback.print_exc()
    
    def test_load_file(self):
        """Test loading a video file"""
        if not self.video_player:
            self.status_label.setText("❌ Create video player first")
            return
        
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "Select Video File",
            "",
            "Video Files (*.mp4 *.avi *.mov *.mkv *.wmv *.flv *.webm *.m4v *.3gp);;All Files (*)"
        )
        
        if file_path:
            try:
                self.status_label.setText(f"Loading: {os.path.basename(file_path)}")
                self.video_player.load(file_path)
                self.status_label.setText(f"✅ Loaded: {os.path.basename(file_path)}")
            except Exception as e:
                self.status_label.setText(f"❌ Load error: {str(e)}")
                print(f"Load error: {e}")
    
    def test_premiere(self):
        """Test opening Premiere interface"""
        try:
            self.status_label.setText("Opening Premiere interface...")
            
            from ui.premiere_interface import PremiereInterface
            self.premiere = PremiereInterface()
            self.premiere.show()
            
            self.status_label.setText("✅ Premiere interface opened!")
            
        except Exception as e:
            self.status_label.setText(f"❌ Premiere error: {str(e)}")
            print(f"Premiere error: {e}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    print("🧪 Starting video playback debug...")
    print("=" * 50)
    
    # Test 1: Qt Multimedia
    qt_ok = test_qt_multimedia()
    
    # Test 2: VideoPlayerWidget
    player_ok = test_video_player_widget()
    
    # Test 3: PremiereInterface
    premiere_ok = test_premiere_interface()
    
    # Test 4: File loading
    file_ok = test_file_loading()
    
    print("\n" + "=" * 50)
    print("🏁 Debug Summary:")
    print(f"✅ Qt Multimedia: {'Working' if qt_ok else 'Failed'}")
    print(f"✅ VideoPlayerWidget: {'Working' if player_ok else 'Failed'}")
    print(f"✅ PremiereInterface: {'Working' if premiere_ok else 'Failed'}")
    print(f"✅ File Loading: {'Working' if file_ok else 'Failed'}")
    
    # Recommendations
    print("\n🔧 Recommendations:")
    if not qt_ok:
        print("- Install Qt multimedia: pip install PyQt5")
        print("- Check system codecs")
    
    if not player_ok:
        print("- Check VideoPlayerWidget implementation")
        print("- Verify PyQt5 multimedia widgets")
    
    if not premiere_ok:
        print("- Check PremiereInterface imports")
        print("- Verify all dependencies")
    
    if not file_ok:
        print("- Add test video files to test_videos folder")
        print("- Check file permissions")
    
    # Start GUI test if requested
    if len(sys.argv) > 1 and sys.argv[1] == "--gui":
        print("\n🖥️ Starting GUI test...")
        app = QApplication(sys.argv)
        window = VideoTestWindow()
        window.show()
        sys.exit(app.exec_())
