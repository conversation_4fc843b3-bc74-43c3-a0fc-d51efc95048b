# -*- coding: utf-8 -*-
# Lists available video formats from URLs

from typing import Dict, List, Union, Optional, Any
from dataclasses import dataclass
import json
from datetime import datetime

@dataclass
class VideoFormat:
    format_id: str
    ext: str
    resolution: str
    fps: Optional[int]
    filesize: Optional[int]
    vcodec: Optional[str]
    acodec: Optional[str]
    format_note: str
    quality: int  # 1-10 quality rating

# Mock utils if not available
try:
    from utils.video_downloader import list_formats as yt_list_formats
    from utils.helpers import get_cache, set_cache
except ImportError:
    # Create mock functions for development/testing
    def yt_list_formats(url):
        """Mock function for testing"""
        return [
            {
                'format_id': '18', 'ext': 'mp4', 'resolution': '640x360',
                'fps': 30, 'filesize': 10000000, 'vcodec': 'h264',
                'acodec': 'aac', 'format_note': '360p', 'height': 360
            },
            {
                'format_id': '22', 'ext': 'mp4', 'resolution': '1280x720',
                'fps': 30, 'filesize': 20000000, 'vcodec': 'h264',
                'acodec': 'aac', 'format_note': '720p', 'height': 720
            }
        ]
    
    cache_store = {}
    
    def get_cache(key):
        return cache_store.get(key)
    
    def set_cache(key, value, timeout=None):
        cache_store[key] = value

def formats(url: str, cache: bool = True) -> Dict[str, Union[List[VideoFormat], str]]:
    """
    Retrieve available video formats for a given URL.
    
    Args:
        url: URL of the video to analyze
        cache: Whether to use cached format data if available
        
    Returns:
        Dictionary with 'success' status, 'formats' list, and optional 'error' message
    """
    try:
        # Check cache first
        cache_key = f"formats_{hash(url)}"
        if cache:
            cached_data = get_cache(cache_key)
            if cached_data:
                return {
                    "success": True,
                    "formats": cached_data,
                    "source_url": url,
                    "cached": True
                }
        
        # Get raw format data from youtube-dl
        raw_formats = yt_list_formats(url)
        
        # Process and standardize the format data
        processed_formats = []
        for fmt in raw_formats:
            # Calculate quality score (1-10)
            quality_score = 1
            if '1080' in fmt.get('resolution', ''):
                quality_score = 8
            elif '720' in fmt.get('resolution', ''):
                quality_score = 6
            elif '480' in fmt.get('resolution', ''):
                quality_score = 4
            
            if fmt.get('height', 0) >= 2160:
                quality_score = 10
            elif fmt.get('height', 0) >= 1440:
                quality_score = 9
            
            # Adjust based on codec
            if 'av01' in (fmt.get('vcodec') or ''):
                quality_score += 1
            elif 'h265' in (fmt.get('vcodec') or '') or 'hevc' in (fmt.get('vcodec') or ''):
                quality_score += 1
            
            # Ensure quality score is between 1-10
            quality_score = max(1, min(10, quality_score))
            
            processed_formats.append(
                VideoFormat(
                    format_id=fmt.get('format_id', ''),
                    ext=fmt.get('ext', ''),
                    resolution=fmt.get('resolution', 'unknown'),
                    fps=fmt.get('fps'),
                    filesize=fmt.get('filesize'),
                    vcodec=fmt.get('vcodec'),
                    acodec=fmt.get('acodec'),
                    format_note=fmt.get('format_note', ''),
                    quality=quality_score
                )
            )
        
        # Sort by quality (highest first)
        processed_formats.sort(key=lambda x: x.quality, reverse=True)
        
        # Cache the results
        set_cache(cache_key, processed_formats, timeout=3600)  # Cache for 1 hour
        
        return {
            "success": True,
            "formats": processed_formats,
            "source_url": url,
            "cached": False
        }
        
    except Exception as e:
        return {
            "success": False,
            "error": f"Failed to retrieve formats: {str(e)}",
            "source_url": url
        }

def get_recommended_format(url: str) -> Dict[str, Any]:
    """
    Get the recommended format for a video based on quality and file size.
    
    Args:
        url: URL of the video to analyze
        
    Returns:
        Dictionary with recommended format information
    """
    result = formats(url)
    
    if not result["success"]:
        return result
    
    formats_list = result["formats"]
    
    if not formats_list:
        return {
            "success": False,
            "error": "No formats available",
            "source_url": url
        }
    
    # Find the best balance of quality and reasonable file size
    best_format = None
    best_score = -1
    
    for fmt in formats_list:
        # Score based on quality and file size (smaller files get a bonus)
        size_factor = 1
        if fmt.filesize:
            # Prefer formats under 100MB
            if fmt.filesize < 100 * 1024 * 1024:
                size_factor = 1.5
            elif fmt.filesize > 500 * 1024 * 1024:
                size_factor = 0.7
        
        score = fmt.quality * size_factor
        
        if score > best_score:
            best_score = score
            best_format = fmt
    
    return {
        "success": True,
        "recommended_format": best_format,
        "source_url": url,
        "score": best_score
    }
