#!/usr/bin/env python3
"""
Debug search functionality
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_yt_dlp_search():
    """Test yt-dlp search functionality"""
    try:
        import yt_dlp
        print("✅ yt-dlp imported successfully")
        
        query = 'Imran'
        print(f"🔍 Testing search for: {query}")
        
        ydl_opts = {
            'quiet': False,
            'no_warnings': False,
            'extract_flat': True,
            'ignoreerrors': True,
            'no_check_certificate': True,
            'socket_timeout': 30,
            'retries': 3,
        }

        # Format search query properly
        search_query = f"ytsearch5:{query}"
        print(f"Using search query: {search_query}")

        with yt_dlp.YoutubeDL(ydl_opts) as ydl:
            search_results = ydl.extract_info(search_query, download=False)
        
        print(f"Search results type: {type(search_results)}")
        if search_results:
            print(f"Keys: {list(search_results.keys()) if hasattr(search_results, 'keys') else 'No keys'}")
            if 'entries' in search_results:
                print(f"Number of entries: {len(search_results['entries'])}")
                for i, entry in enumerate(search_results['entries'][:3]):
                    if entry:
                        print(f"  {i+1}. {entry.get('title', 'No title')} - {entry.get('id', 'No ID')}")
                    else:
                        print(f"  {i+1}. Empty entry")
            else:
                print("❌ No 'entries' found in search results")
        else:
            print("❌ No search results returned")
            
        return search_results
        
    except Exception as e:
        print(f"❌ Search failed: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_search_interface():
    """Test the search interface"""
    try:
        from ui.search_interface import VideoSearchWidget
        print("✅ VideoSearchWidget imported successfully")
        
        # Test search thread
        from ui.search_interface import SearchThread
        print("✅ SearchThread imported successfully")
        
        # Create a test search
        search_thread = SearchThread("Imran", "YouTube", 5)
        print("✅ SearchThread created successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ Search interface test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_video_downloader():
    """Test video downloader module"""
    try:
        from utils.video_downloader import search_videos
        print("✅ video_downloader imported successfully")
        
        results = search_videos('Imran', platform='youtube', max_results=3)
        print(f"video_downloader results: {len(results) if results else 0} videos")
        
        if results:
            for i, video in enumerate(results[:3]):
                print(f"  {i+1}. {video.get('title', 'No title')}")
        
        return results
        
    except Exception as e:
        print(f"❌ video_downloader test failed: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_social_search():
    """Test social search module"""
    try:
        from social.search import search_videos, Platform
        print("✅ social search imported successfully")
        
        result = search_videos('Imran', platform=Platform.YOUTUBE, max_results=3)
        print(f"Social search success: {result.get('success', False)}")
        
        if result.get('success'):
            videos = result.get('results', [])
            print(f"Social search results: {len(videos)} videos")
            for i, video in enumerate(videos[:3]):
                print(f"  {i+1}. {video.get('title', 'No title')}")
        else:
            print(f"Social search error: {result.get('error', 'Unknown error')}")
        
        return result
        
    except Exception as e:
        print(f"❌ social search test failed: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    print("🧪 Starting search functionality debug...")
    print("=" * 50)
    
    # Test 1: Direct yt-dlp
    print("\n1. Testing yt-dlp directly:")
    yt_results = test_yt_dlp_search()
    
    # Test 2: Search interface
    print("\n2. Testing search interface:")
    interface_ok = test_search_interface()
    
    # Test 3: Video downloader
    print("\n3. Testing video downloader:")
    downloader_results = test_video_downloader()
    
    # Test 4: Social search
    print("\n4. Testing social search:")
    social_results = test_social_search()
    
    print("\n" + "=" * 50)
    print("🏁 Debug Summary:")
    print(f"✅ yt-dlp direct: {'Working' if yt_results else 'Failed'}")
    print(f"✅ Search interface: {'Working' if interface_ok else 'Failed'}")
    print(f"✅ Video downloader: {'Working' if downloader_results else 'Failed'}")
    print(f"✅ Social search: {'Working' if social_results and social_results.get('success') else 'Failed'}")
    
    # Recommendations
    print("\n🔧 Recommendations:")
    if not yt_results:
        print("- Check internet connection")
        print("- Update yt-dlp: pip install --upgrade yt-dlp")
    
    if not interface_ok:
        print("- Check PyQt5 installation")
        print("- Verify import paths")
    
    if not downloader_results:
        print("- Check video_downloader module")
        print("- Verify dependencies")
    
    if not social_results or not social_results.get('success'):
        print("- Check social search module")
        print("- Verify API connections")
