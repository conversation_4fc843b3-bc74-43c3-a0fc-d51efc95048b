# -*- coding: utf-8 -*-
# Video downloading functionality

from typing import Dict, Any, Optional, Tuple
from pathlib import Path
import tempfile
import os
import json
from datetime import datetime

# Mock utils if not available
try:
    from utils.video_downloader import download_video_standalone as yt_download, download_playlist_standalone as yt_download_playlist
    from utils.helpers import generate_unique_filename
except ImportError:
    # Mock functions for development
    def yt_download(url, **kwargs):
        """Mock download function"""
        output_path = kwargs.get('outtmpl', 'downloaded_video.mp4')
        # Create a dummy file
        with open(output_path, 'w') as f:
            f.write("Mock video content")
        return output_path
    
    def yt_download_playlist(url, format_id='best', max_downloads=10):
        """Mock playlist download"""
        return [f"video_{i}.mp4" for i in range(min(max_downloads, 3))]
    
    def generate_unique_filename(prefix, extension):
        """Mock unique filename generator"""
        return f"{prefix}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.{extension}"

def download_video(
    url: str, 
    format_id: str = 'best', 
    start_time: Optional[float] = None,
    end_time: Optional[float] = None,
    output_path: Optional[str] = None,
    metadata: Optional[Dict] = None
) -> Dict[str, Any]:
    """
    Download a video with optional trimming and quality selection.
    
    Args:
        url: URL of the video to download
        format_id: Format/quality identifier (default: 'best')
        start_time: Start time for trimming (in seconds)
        end_time: End time for trimming (in seconds)
        output_path: Custom output path for the downloaded file
        metadata: Additional metadata to store with the video
        
    Returns:
        Dictionary with download status and file information
    """
    try:
        # Set up download options
        download_options = {
            'format': format_id,
            'outtmpl': output_path or generate_unique_filename('video', 'mp4')
        }
        
        # Add trimming options if specified
        if start_time is not None or end_time is not None:
            download_options['postprocessor_args'] = []
            if start_time is not None:
                download_options['postprocessor_args'].extend(['-ss', str(start_time)])
            if end_time is not None:
                download_options['postprocessor_args'].extend(['-to', str(end_time)])
        
        # Download the video
        filepath = yt_download(url, **download_options)
        
        # Get file info
        file_stats = os.stat(filepath)
        
        # Save metadata if provided
        metadata_file = None
        if metadata:
            metadata_file = f"{filepath}.meta.json"
            metadata.update({
                "download_date": datetime.now().isoformat(),
                "source_url": url,
                "format_id": format_id
            })
            with open(metadata_file, 'w') as f:
                json.dump(metadata, f, indent=2)
        
        return {
            "success": True,
            "filepath": filepath,
            "filename": os.path.basename(filepath),
            "filesize": file_stats.st_size,
            "format_id": format_id,
            "trimmed": start_time is not None or end_time is not None,
            "start_time": start_time,
            "end_time": end_time,
            "metadata_file": metadata_file,
            "metadata": metadata or {}
        }
        
    except Exception as e:
        return {
            "success": False,
            "error": f"Download failed: {str(e)}",
            "url": url,
            "format_id": format_id
        }

def download_best(url: str, **kwargs) -> Dict[str, Any]:
    """
    Convenience function to download the best quality version of a video.
    
    Args:
        url: URL of the video to download
        **kwargs: Additional arguments passed to download_video
        
    Returns:
        Dictionary with download status and file information
    """
    return download_video(url, format_id='best', **kwargs)

def download_playlist(url: str, format_id: str = 'best', max_videos: int = 10) -> Dict[str, Any]:
    """
    Download an entire playlist or multiple videos from a channel.
    
    Args:
        url: URL of the playlist or channel
        format_id: Format/quality identifier
        max_videos: Maximum number of videos to download
        
    Returns:
        Dictionary with download status and information about downloaded videos
    """
    try:
        results = yt_download_playlist(url, format_id=format_id, max_downloads=max_videos)
        
        return {
            "success": True,
            "downloaded_videos": results,
            "total_count": len(results),
            "playlist_url": url
        }
        
    except Exception as e:
        return {
            "success": False,
            "error": f"Playlist download failed: {str(e)}",
            "playlist_url": url
        }
