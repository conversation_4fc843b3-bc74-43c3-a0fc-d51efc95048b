#!/usr/bin/env python3
"""
Test script for the new Adobe Premiere Pro-style interface
"""

import sys
import os

# Add VideoApp to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'VideoApp'))

from PyQt5.QtWidgets import QApplication
from VideoApp.ui.premiere_interface import PremiereInterface

def main():
    """Test the Premiere Pro-style interface"""
    app = QApplication(sys.argv)
    
    print("🎬 Testing Adobe Premiere Pro-style Interface")
    print("=" * 50)
    
    # Create the interface
    window = PremiereInterface()
    window.show()
    
    print("✅ Interface Features:")
    print("   📁 Project Panel - Manage media files")
    print("   🎨 Effects Panel - Video/Audio effects and transitions")
    print("   🎬 Video Player - Improved crash protection")
    print("   📊 Properties Panel - Adjust clip properties")
    print("   🎞️ Timeline - Separate tracks for:")
    print("      • Video tracks (blue)")
    print("      • Audio tracks (green)")
    print("      • Text tracks (orange)")
    print("      • Image tracks (purple)")
    print("   🔧 Professional controls:")
    print("      • Mute/Solo buttons for audio")
    print("      • Lock tracks to prevent editing")
    print("      • Zoom in/out timeline")
    print("      • Add new tracks dynamically")
    
    print("\n🎯 How to use:")
    print("   1. Drag media files to project panel")
    print("   2. Drag from project to timeline tracks")
    print("   3. Use effects panel to add effects")
    print("   4. Adjust properties in properties panel")
    print("   5. Use timeline controls for editing")
    
    print("\n🚀 Starting interface...")
    
    # Start the application
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
