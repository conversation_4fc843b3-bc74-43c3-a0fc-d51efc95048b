@echo off
set PYTHON="C:\Program Files\Python313\python.exe"
set PROJECT="D:\BISH\VideoApp"
set ICON="%PROJECT%\assets\app_icon.ico"

echo ========================================
echo    Professional Video Editor Builder
echo ========================================

echo Cleaning previous builds...
cd /d %PROJECT%
rmdir /s /q build 2>nul
rmdir /s /q dist 2>nul
del VideoApp.spec 2>nul

echo Verifying Python installation...
%PYTHON% --version
if %errorlevel% neq 0 (
    echo Error: Python not found at %PYTHON%
    echo Please update the PYTHON path in this script
    pause
    exit /b 1
)

echo Verifying icon exists...
if not exist %ICON% (
    echo Error: Icon file not found at %ICON%
    echo Please ensure the icon file exists before building
    pause
    exit /b 1
)

echo Installing/Updating PyInstaller...
%PYTHON% -m pip install --upgrade pyinstaller

echo Checking required dependencies...
%PYTHON% -c "import PyQt5; print('PyQt5: OK')" 2>nul || (
    echo Installing PyQt5...
    %PYTHON% -m pip install PyQt5
)

%PYTHON% -c "import yt_dlp; print('yt-dlp: OK')" 2>nul || (
    echo Installing yt-dlp...
    %PYTHON% -m pip install yt-dlp
)

%PYTHON% -c "import requests; print('requests: OK')" 2>nul || (
    echo Installing requests...
    %PYTHON% -m pip install requests
)

echo Building VideoApp executable...
%PYTHON% -m PyInstaller ^
  --onefile ^
  --windowed ^
  --name VideoApp ^
  --icon=%ICON% ^
  --add-data "%PROJECT%\assets;assets" ^
  --add-data "%PROJECT%\core;core" ^
  --add-data "%PROJECT%\dashboard;dashboard" ^
  --add-data "%PROJECT%\ui;ui" ^
  --add-data "%PROJECT%\utils;utils" ^
  --add-data "%PROJECT%\editing;editing" ^
  --add-data "%PROJECT%\export;export" ^
  --add-data "%PROJECT%\social;social" ^

  --hidden-import=PyQt5 ^
  --hidden-import=PyQt5.QtCore ^
  --hidden-import=PyQt5.QtWidgets ^
  --hidden-import=PyQt5.QtGui ^
  --hidden-import=PyQt5.QtMultimedia ^
  --hidden-import=PyQt5.QtMultimediaWidgets ^
  --hidden-import=PyQt5.QtWebEngineWidgets ^
  --hidden-import=yt_dlp ^
  --hidden-import=requests ^
  --hidden-import=json ^
  --hidden-import=threading ^
  --hidden-import=urllib.parse ^
  --hidden-import=datetime ^
  --hidden-import=os ^
  --hidden-import=sys ^
  --hidden-import=time ^
  --hidden-import=re ^
  --hidden-import=typing ^
  --hidden-import=concurrent.futures ^
  --collect-all=yt_dlp ^
  --collect-submodules=PyQt5 ^
  --clean ^
  --noconfirm ^
  main.py

if %errorlevel% neq 0 (
    echo Build failed! Check the error messages above.
    pause
    exit /b 1
)

echo ========================================
echo Build completed successfully!
echo ========================================
echo Executable location: %PROJECT%\dist\VideoApp.exe
echo File size:
dir "%PROJECT%\dist\VideoApp.exe" | find "VideoApp.exe"

echo.
echo Testing the executable...
echo Starting VideoApp.exe for 5 seconds...
start "" "%PROJECT%\dist\VideoApp.exe"
timeout /t 5 /nobreak >nul
taskkill /f /im VideoApp.exe 2>nul

echo.
echo ========================================
echo Build Summary:
echo ========================================
echo - Executable: VideoApp.exe
echo - Location: %PROJECT%\dist\
echo - Icon: Included
echo - Dependencies: Bundled
echo - Type: Standalone (no Python required)
echo ========================================

echo.
echo You can now distribute VideoApp.exe as a standalone application!
echo.
pause
