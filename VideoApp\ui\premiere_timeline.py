"""
Adobe Premiere Pro-style Timeline Editor
Professional video editing interface with separate tracks for video, audio, text, and images
"""

import sys
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *
import json
import os

class TimelineTrack(QWidget):
    """Individual track in the timeline (video, audio, text, image)"""
    
    def __init__(self, track_type, track_name, parent=None):
        super().__init__(parent)
        self.track_type = track_type  # 'video', 'audio', 'text', 'image'
        self.track_name = track_name
        self.clips = []
        self.is_locked = False
        self.is_muted = False
        self.is_solo = False
        
        self.setup_ui()
        
    def setup_ui(self):
        """Setup track UI"""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        
        # Track header (controls)
        self.header = self.create_track_header()
        layout.addWidget(self.header)
        
        # Track content area (clips)
        self.content_area = QWidget()
        self.content_area.setMinimumHeight(60)
        self.content_area.setStyleSheet(f"""
            QWidget {{
                background-color: {self.get_track_color()};
                border: 1px solid #444;
                border-left: none;
            }}
        """)
        layout.addWidget(self.content_area, 1)
        
        # Enable drag and drop
        self.content_area.setAcceptDrops(True)

        # Override drag and drop events for content area
        self.content_area.dragEnterEvent = self.dragEnterEvent
        self.content_area.dragMoveEvent = self.dragMoveEvent
        self.content_area.dropEvent = self.dropEvent
        
    def create_track_header(self):
        """Create track header with controls"""
        header = QWidget()
        header.setFixedWidth(200)
        header.setStyleSheet("""
            QWidget {
                background-color: #2b2b2b;
                border: 1px solid #444;
                border-right: none;
            }
            QPushButton {
                background-color: #404040;
                border: 1px solid #555;
                color: white;
                padding: 4px;
                margin: 1px;
            }
            QPushButton:hover {
                background-color: #505050;
            }
            QPushButton:pressed {
                background-color: #606060;
            }
            QLabel {
                color: white;
                font-weight: bold;
                padding: 4px;
            }
        """)
        
        layout = QVBoxLayout(header)
        layout.setContentsMargins(4, 4, 4, 4)
        
        # Track name and type
        name_layout = QHBoxLayout()
        icon_label = QLabel(self.get_track_icon())
        icon_label.setFixedSize(16, 16)
        name_label = QLabel(self.track_name)
        name_layout.addWidget(icon_label)
        name_layout.addWidget(name_label)
        name_layout.addStretch()
        layout.addLayout(name_layout)
        
        # Control buttons
        controls_layout = QHBoxLayout()
        
        # Mute/Solo buttons (for audio tracks)
        if self.track_type in ['audio', 'video']:
            self.mute_btn = QPushButton("M")
            self.mute_btn.setFixedSize(24, 24)  # Increased size
            self.mute_btn.setCheckable(True)
            self.mute_btn.setStyleSheet("""
                QPushButton {
                    background-color: #404040;
                    border: 1px solid #555;
                    color: white;
                    font-weight: bold;
                    font-size: 10px;
                    border-radius: 3px;
                }
                QPushButton:hover {
                    background-color: #505050;
                }
                QPushButton:checked {
                    background-color: #d4af37;
                    color: black;
                }
            """)
            self.mute_btn.clicked.connect(self.toggle_mute)
            controls_layout.addWidget(self.mute_btn)

            self.solo_btn = QPushButton("S")
            self.solo_btn.setFixedSize(24, 24)  # Increased size
            self.solo_btn.setCheckable(True)
            self.solo_btn.setStyleSheet("""
                QPushButton {
                    background-color: #404040;
                    border: 1px solid #555;
                    color: white;
                    font-weight: bold;
                    font-size: 10px;
                    border-radius: 3px;
                }
                QPushButton:hover {
                    background-color: #505050;
                }
                QPushButton:checked {
                    background-color: #ff6b35;
                    color: white;
                }
            """)
            self.solo_btn.clicked.connect(self.toggle_solo)
            controls_layout.addWidget(self.solo_btn)

        # Lock button
        self.lock_btn = QPushButton("L")  # Changed from emoji to letter for better visibility
        self.lock_btn.setFixedSize(24, 24)  # Increased size
        self.lock_btn.setCheckable(True)
        self.lock_btn.setStyleSheet("""
            QPushButton {
                background-color: #404040;
                border: 1px solid #555;
                color: white;
                font-weight: bold;
                font-size: 10px;
                border-radius: 3px;
            }
            QPushButton:hover {
                background-color: #505050;
            }
            QPushButton:checked {
                background-color: #ff4444;
                color: white;
            }
        """)
        self.lock_btn.clicked.connect(self.toggle_lock)
        controls_layout.addWidget(self.lock_btn)
        
        controls_layout.addStretch()
        layout.addLayout(controls_layout)

        return header

    def dragEnterEvent(self, event):
        """Handle drag enter event"""
        if event.mimeData().hasUrls():
            urls = event.mimeData().urls()
            if urls and len(urls) > 0:
                file_path = urls[0].toLocalFile()
                if file_path and self.is_compatible_file(file_path):
                    event.acceptProposedAction()
                    self.show_drop_indicator()
                    return
        elif event.mimeData().hasText():
            # Handle text drops for text tracks
            if self.track_type == 'text':
                event.acceptProposedAction()
                self.show_drop_indicator()
                return
        event.ignore()

    def dragMoveEvent(self, event):
        """Handle drag move event"""
        if event.mimeData().hasUrls() or (event.mimeData().hasText() and self.track_type == 'text'):
            event.acceptProposedAction()
        else:
            event.ignore()

    def dropEvent(self, event):
        """Handle drop event"""
        self.hide_drop_indicator()

        if event.mimeData().hasUrls():
            urls = event.mimeData().urls()
            if urls and len(urls) > 0:
                file_path = urls[0].toLocalFile()
                if file_path and self.is_compatible_file(file_path):
                    # Calculate drop position
                    drop_position = event.pos().x()
                    self.add_clip_at_position(file_path, drop_position)
                    event.acceptProposedAction()
                    return
        elif event.mimeData().hasText() and self.track_type == 'text':
            # Handle text drop
            text_content = event.mimeData().text()
            drop_position = event.pos().x()
            self.add_text_clip_at_position(text_content, drop_position)
            event.acceptProposedAction()
            return

        event.ignore()

    def is_compatible_file(self, file_path):
        """Check if file is compatible with this track type"""
        if not file_path:
            return False

        file_ext = file_path.lower().split('.')[-1] if '.' in file_path else ''

        if self.track_type == 'video':
            return file_ext in ['mp4', 'avi', 'mov', 'mkv', 'wmv', 'flv', 'webm', 'm4v', '3gp']
        elif self.track_type == 'audio':
            return file_ext in ['mp3', 'wav', 'aac', 'flac', 'ogg', 'm4a', 'wma']
        elif self.track_type == 'image':
            return file_ext in ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'tiff', 'webp']
        elif self.track_type == 'text':
            return file_ext in ['txt', 'srt', 'vtt', 'ass'] or True  # Accept any file for text

        return False

    def show_drop_indicator(self):
        """Show visual drop indicator"""
        self.content_area.setStyleSheet(f"""
            QWidget {{
                background-color: {self.get_track_color()};
                border: 2px dashed #0078d4;
                border-left: none;
            }}
        """)

    def hide_drop_indicator(self):
        """Hide visual drop indicator"""
        self.content_area.setStyleSheet(f"""
            QWidget {{
                background-color: {self.get_track_color()};
                border: 1px solid #444;
                border-left: none;
            }}
        """)

    def add_clip_at_position(self, file_path, x_position):
        """Add a clip at the specified position"""
        # Convert x position to time (assuming 20 pixels per second)
        time_position = x_position / 20.0

        # Create clip data
        clip_data = {
            'file_path': file_path,
            'start_time': time_position,
            'duration': 5.0,  # Default duration
            'track_type': self.track_type
        }

        self.clips.append(clip_data)
        print(f"Added {self.track_type} clip: {file_path} at {time_position:.2f}s")

        # Emit signal to parent timeline
        if hasattr(self.parent(), 'clip_added'):
            self.parent().clip_added.emit(clip_data)

    def add_text_clip_at_position(self, text_content, x_position):
        """Add a text clip at the specified position"""
        time_position = x_position / 20.0

        clip_data = {
            'text_content': text_content,
            'start_time': time_position,
            'duration': 3.0,  # Default text duration
            'track_type': 'text'
        }

        self.clips.append(clip_data)
        print(f"Added text clip: '{text_content}' at {time_position:.2f}s")

        if hasattr(self.parent(), 'clip_added'):
            self.parent().clip_added.emit(clip_data)
        
    def get_track_icon(self):
        """Get icon for track type"""
        icons = {
            'video': '🎬',
            'audio': '🔊',
            'text': '📝',
            'image': '🖼️'
        }
        return icons.get(self.track_type, '📁')
        
    def get_track_color(self):
        """Get color for track type"""
        colors = {
            'video': '#1a4d66',
            'audio': '#4d661a',
            'text': '#664d1a',
            'image': '#661a4d'
        }
        return colors.get(self.track_type, '#333333')
        
    def toggle_mute(self):
        """Toggle mute state"""
        self.is_muted = self.mute_btn.isChecked()
        self.mute_btn.setStyleSheet(
            "background-color: #ff4444;" if self.is_muted else ""
        )
        
    def toggle_solo(self):
        """Toggle solo state"""
        self.is_solo = self.solo_btn.isChecked()
        self.solo_btn.setStyleSheet(
            "background-color: #ffaa00;" if self.is_solo else ""
        )
        
    def toggle_lock(self):
        """Toggle lock state"""
        self.is_locked = self.lock_btn.isChecked()
        self.lock_btn.setStyleSheet(
            "background-color: #aa4444;" if self.is_locked else ""
        )
        self.content_area.setEnabled(not self.is_locked)

class TimelineRuler(QWidget):
    """Timeline ruler showing time markers"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setFixedHeight(30)
        self.duration = 3600  # seconds
        self.zoom_level = 1.0
        self.current_time = 0
        
    def paintEvent(self, event):
        """Paint the timeline ruler"""
        painter = QPainter(self)
        painter.fillRect(self.rect(), QColor(40, 40, 40))
        
        # Draw time markers
        painter.setPen(QPen(QColor(200, 200, 200), 1))
        font = QFont("Arial", 8)
        painter.setFont(font)
        
        width = self.width() - 200  # Account for track headers
        if width <= 0:
            return
            
        # Calculate time intervals
        pixels_per_second = width / self.duration * self.zoom_level
        
        # Draw major markers (every 10 seconds)
        for i in range(0, self.duration, 10):
            x = 200 + i * pixels_per_second
            if x > self.width():
                break
                
            painter.drawLine(int(x), 0, int(x), 20)
            
            # Time text
            minutes = i // 60
            seconds = i % 60
            time_text = f"{minutes:02d}:{seconds:02d}"
            painter.drawText(int(x) + 2, 15, time_text)
        
        # Draw playhead
        playhead_x = 200 + self.current_time * pixels_per_second
        painter.setPen(QPen(QColor(255, 100, 100), 2))
        painter.drawLine(int(playhead_x), 0, int(playhead_x), self.height())

    def mousePressEvent(self, event):
        """Handle mouse press to move playhead"""
        if event.button() == Qt.LeftButton:
            # Calculate time from mouse position
            pixels_per_second = 50 * self.zoom_level
            time_seconds = max(0, (event.x() - 200) / pixels_per_second)
            time_seconds = min(time_seconds, self.duration)

            # Update current time and emit signal
            self.current_time = time_seconds
            self.update()  # Redraw ruler

            # Notify parent timeline
            if self.parent():
                self.parent().set_playhead_position(time_seconds)

class PremiereTimeline(QWidget):
    """Main timeline widget with multiple tracks"""

    # Signals
    clip_added = pyqtSignal(object)  # Emits clip data when a clip is added
    playhead_moved = pyqtSignal(float)  # Emits time in seconds when playhead moves

    def __init__(self, parent=None):
        super().__init__(parent)
        self.tracks = []
        self.current_time = 0.0  # Current playhead position in seconds
        self.duration = 3600.0  # Total timeline duration in seconds
        self.setup_ui()
        self.create_default_tracks()
        
    def setup_ui(self):
        """Setup timeline UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        
        # Timeline ruler
        self.ruler = TimelineRuler()
        layout.addWidget(self.ruler)
        
        # Tracks container
        self.tracks_container = QWidget()
        self.tracks_layout = QVBoxLayout(self.tracks_container)
        self.tracks_layout.setContentsMargins(0, 0, 0, 0)
        self.tracks_layout.setSpacing(1)
        
        # Scroll area for tracks
        scroll_area = QScrollArea()
        scroll_area.setWidget(self.tracks_container)
        scroll_area.setWidgetResizable(True)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setMinimumHeight(150)  # Ensure minimum height
        layout.addWidget(scroll_area, 1)  # Give it stretch factor 1

        # Timeline controls - always visible at bottom
        controls = self.create_timeline_controls()
        layout.addWidget(controls, 0)  # No stretch, fixed size
        
    def create_timeline_controls(self):
        """Create timeline control buttons"""
        controls = QWidget()
        controls.setFixedHeight(50)  # Increased height for better visibility
        controls.setStyleSheet("""
            QWidget {
                background-color: #2b2b2b;
                border-top: 2px solid #444;
                border-bottom: 1px solid #444;
            }
            QPushButton {
                background-color: #404040;
                border: 1px solid #555;
                color: white;
                padding: 8px 16px;
                margin: 6px 4px;
                border-radius: 4px;
                font-weight: bold;
                min-height: 24px;
            }
            QPushButton:hover {
                background-color: #505050;
                border: 1px solid #666;
            }
            QPushButton:pressed {
                background-color: #606060;
            }
        """)
        
        layout = QHBoxLayout(controls)
        
        # Add track buttons
        add_video_btn = QPushButton("+ Video Track")
        add_video_btn.clicked.connect(lambda: self.add_track('video'))
        layout.addWidget(add_video_btn)
        
        add_audio_btn = QPushButton("+ Audio Track")
        add_audio_btn.clicked.connect(lambda: self.add_track('audio'))
        layout.addWidget(add_audio_btn)
        
        add_text_btn = QPushButton("+ Text Track")
        add_text_btn.clicked.connect(lambda: self.add_track('text'))
        layout.addWidget(add_text_btn)
        
        add_image_btn = QPushButton("+ Image Track")
        add_image_btn.clicked.connect(lambda: self.add_track('image'))
        layout.addWidget(add_image_btn)
        
        layout.addStretch()
        
        # Zoom controls
        zoom_out_btn = QPushButton("🔍-")
        zoom_out_btn.clicked.connect(self.zoom_out)
        layout.addWidget(zoom_out_btn)
        
        zoom_in_btn = QPushButton("🔍+")
        zoom_in_btn.clicked.connect(self.zoom_in)
        layout.addWidget(zoom_in_btn)
        
        return controls
        
    def create_default_tracks(self):
        """Create default tracks"""
        # Video tracks
        self.add_track('video', 'Video 1')
        self.add_track('video', 'Video 2')
        
        # Audio tracks
        self.add_track('audio', 'Audio 1')
        self.add_track('audio', 'Audio 2')
        
        # Text track
        self.add_track('text', 'Text 1')
        
        # Image track
        self.add_track('image', 'Images 1')
        
    def add_track(self, track_type, track_name=None):
        """Add a new track"""
        if not track_name:
            count = len([t for t in self.tracks if t.track_type == track_type]) + 1
            track_name = f"{track_type.title()} {count}"

        track = TimelineTrack(track_type, track_name, self)
        self.tracks.append(track)
        self.tracks_layout.addWidget(track)

        # Connect track signals
        if hasattr(track, 'clip_added'):
            track.clip_added.connect(self.on_clip_added)

    def on_clip_added(self, clip_data):
        """Handle clip added to track"""
        print(f"Timeline: Clip added - {clip_data}")
        self.clip_added.emit(clip_data)

    def add_clip(self, file_path, track_type='video'):
        """Add a clip to the timeline"""
        try:
            # Find the appropriate track
            target_track = None
            for track in self.tracks:
                if track.track_type == track_type:
                    target_track = track
                    break

            if not target_track:
                # Create a new track if none exists
                self.add_track(track_type)
                target_track = self.tracks[-1]

            # Create clip representation
            clip_info = {
                'file_path': file_path,
                'name': os.path.basename(file_path),
                'duration': 0,  # Will be updated when video is analyzed
                'start_time': 0,
                'track_type': track_type
            }

            target_track.clips.append(clip_info)
            print(f"✅ Added clip to {track_type} track: {os.path.basename(file_path)}")

        except Exception as e:
            print(f"❌ Error adding clip to timeline: {e}")

    def clear_timeline(self):
        """Clear all clips from timeline"""
        for track in self.tracks:
            track.clips.clear()
        print("🗑️ Timeline cleared")

    def zoom_in(self):
        """Zoom in timeline"""
        self.ruler.zoom_level = min(self.ruler.zoom_level * 1.5, 10.0)
        self.ruler.update()
        
    def zoom_out(self):
        """Zoom out timeline"""
        self.ruler.zoom_level = max(self.ruler.zoom_level / 1.5, 0.1)
        self.ruler.update()

    def set_playhead_position(self, time_seconds):
        """Set playhead position and emit signal"""
        self.current_time = max(0, min(time_seconds, self.duration))
        self.ruler.current_time = self.current_time
        self.ruler.update()

        # Emit signal for video player synchronization
        self.playhead_moved.emit(self.current_time)
        print(f"🎬 Playhead moved to: {self.current_time:.2f}s")
