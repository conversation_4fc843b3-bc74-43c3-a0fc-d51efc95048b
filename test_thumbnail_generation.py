#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script for thumbnail generation functionality
"""

import os
import sys
import tempfile
import subprocess

def test_ffmpeg_available():
    """Test if FFmpeg is available"""
    try:
        result = subprocess.run(["ffmpeg", "-version"], capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            print("✅ FFmpeg is available")
            return True
        else:
            print("❌ FFmpeg not working properly")
            return False
    except FileNotFoundError:
        print("❌ FFmpeg not found in PATH")
        return False
    except Exception as e:
        print(f"❌ FFmpeg test error: {e}")
        return False

def test_moviepy_available():
    """Test if MoviePy is available"""
    try:
        import moviepy.editor as mp
        print("✅ MoviePy is available")
        return True
    except ImportError:
        print("❌ MoviePy not available")
        return False
    except Exception as e:
        print(f"❌ MoviePy test error: {e}")
        return False

def generate_test_thumbnail_ffmpeg(video_path):
    """Test thumbnail generation with FFmpeg"""
    try:
        thumbnail_dir = os.path.join(tempfile.gettempdir(), "bish_test_thumbnails")
        os.makedirs(thumbnail_dir, exist_ok=True)
        thumbnail_path = os.path.join(thumbnail_dir, "test_thumb_ffmpeg.jpg")
        
        cmd = [
            "ffmpeg",
            "-i", video_path,
            "-ss", "00:00:01",
            "-vframes", "1",
            "-vf", "scale=320:240:force_original_aspect_ratio=decrease",
            "-y",
            thumbnail_path
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0 and os.path.exists(thumbnail_path):
            print(f"✅ FFmpeg thumbnail generated: {thumbnail_path}")
            return thumbnail_path
        else:
            print(f"❌ FFmpeg thumbnail failed: {result.stderr}")
            return None
            
    except Exception as e:
        print(f"❌ FFmpeg thumbnail error: {e}")
        return None

def generate_test_thumbnail_moviepy(video_path):
    """Test thumbnail generation with MoviePy"""
    try:
        import moviepy.editor as mp
        from PIL import Image
        
        thumbnail_dir = os.path.join(tempfile.gettempdir(), "bish_test_thumbnails")
        os.makedirs(thumbnail_dir, exist_ok=True)
        thumbnail_path = os.path.join(thumbnail_dir, "test_thumb_moviepy.jpg")
        
        clip = mp.VideoFileClip(video_path)
        duration = clip.duration
        timestamp = min(1.0, duration * 0.1) if duration > 0 else 0
        
        frame = clip.get_frame(timestamp)
        clip.close()
        
        pil_image = Image.fromarray(frame)
        pil_image = pil_image.resize((320, 240), Image.LANCZOS)
        pil_image.save(thumbnail_path, "JPEG", quality=85)
        
        if os.path.exists(thumbnail_path):
            print(f"✅ MoviePy thumbnail generated: {thumbnail_path}")
            return thumbnail_path
        else:
            print("❌ MoviePy thumbnail failed")
            return None
            
    except Exception as e:
        print(f"❌ MoviePy thumbnail error: {e}")
        return None

def main():
    print("🔍 Testing thumbnail generation capabilities...\n")
    
    # Test dependencies
    ffmpeg_available = test_ffmpeg_available()
    moviepy_available = test_moviepy_available()
    
    if not ffmpeg_available and not moviepy_available:
        print("\n❌ Neither FFmpeg nor MoviePy is available!")
        print("📝 To fix this:")
        print("   1. Install FFmpeg: https://ffmpeg.org/download.html")
        print("   2. Or install MoviePy: pip install moviepy")
        return
    
    print(f"\n📊 Summary:")
    print(f"   FFmpeg: {'✅ Available' if ffmpeg_available else '❌ Not available'}")
    print(f"   MoviePy: {'✅ Available' if moviepy_available else '❌ Not available'}")
    
    # Look for test video files
    test_video_paths = []
    
    # Check common video locations
    common_paths = [
        ".",
        "downloads",
        os.path.expanduser("~/Downloads"),
        os.path.expanduser("~/Videos"),
        "C:/Users/<USER>/Videos"
    ]
    
    video_extensions = ['.mp4', '.avi', '.mov', '.mkv', '.wmv']
    
    for path in common_paths:
        if os.path.exists(path):
            try:
                for file in os.listdir(path):
                    if any(file.lower().endswith(ext) for ext in video_extensions):
                        full_path = os.path.join(path, file)
                        if os.path.getsize(full_path) > 1024:  # At least 1KB
                            test_video_paths.append(full_path)
                            if len(test_video_paths) >= 3:  # Limit to 3 test files
                                break
            except (PermissionError, OSError):
                continue
    
    if not test_video_paths:
        print("\n⚠️ No test video files found")
        print("📝 Place a video file in the current directory to test thumbnail generation")
        return
    
    print(f"\n🎬 Found {len(test_video_paths)} test video(s)")
    
    # Test thumbnail generation
    for i, video_path in enumerate(test_video_paths[:1]):  # Test only first video
        print(f"\n🧪 Testing with: {os.path.basename(video_path)}")
        
        if ffmpeg_available:
            print("   Testing FFmpeg method...")
            ffmpeg_result = generate_test_thumbnail_ffmpeg(video_path)
        
        if moviepy_available:
            print("   Testing MoviePy method...")
            moviepy_result = generate_test_thumbnail_moviepy(video_path)
    
    print("\n✅ Thumbnail generation test completed!")

if __name__ == "__main__":
    main()
