@echo off
set PYTHON="C:\Program Files\Python313\python.exe"
set PROJECT="D:\BISH\VideoApp"
set ICON="%PROJECT%\assets\app_icon.ico"

echo Quick Build - Professional Video Editor
echo ========================================

cd /d %PROJECT%

echo Cleaning...
rmdir /s /q dist 2>nul
del VideoApp.spec 2>nul

echo Creating icon if missing...
if not exist %ICON% %PYTHON% create_icon.py

echo Building...
%PYTHON% -m PyInstaller ^
  --onefile ^
  --windowed ^
  --name VideoApp ^
  --icon=%ICON% ^
  --add-data "assets;assets" ^
  --add-data "core;core" ^
  --add-data "dashboard;dashboard" ^
  --add-data "ui;ui" ^
  --add-data "utils;utils" ^
  --add-data "editing;editing" ^
  --add-data "export;export" ^
  --add-data "social;social" ^
  --add-data "download_video;download_video" ^
  --add-data "download_audio;download_audio" ^
  --hidden-import=PyQt5 ^
  --hidden-import=yt_dlp ^
  --hidden-import=requests ^
  --collect-all=yt_dlp ^
  --clean ^
  --noconfirm ^
  main.py

if %errorlevel% equ 0 (
    echo.
    echo ✓ Build successful!
    echo ✓ Location: %PROJECT%\dist\VideoApp.exe
    dir "%PROJECT%\dist\VideoApp.exe" | find "VideoApp.exe"
) else (
    echo.
    echo ✗ Build failed!
)

pause
