# -*- coding: utf-8 -*-
# Audio export module for VideoApp

import os
from typing import Optional
from utils.audio_utils import extract_audio, normalize_audio, add_audio_metadata
from utils.exceptions import ExportError, InvalidFormatError
from utils.video_utils import get_video_duration, get_video_metadata

def export_audio_only(video_path: str, 
                     fmt: str = 'mp3',
                     start_time: Optional[float] = None,
                     end_time: Optional[float] = None,
                     bitrate: Optional[int] = None,
                     output_dir: Optional[str] = None) -> str:
    """
    Extract audio from a video file and export it in the desired format.
    
    Args:
        video_path: Path to the video file
        fmt: Output audio format ('mp3', 'aac', 'wav', 'ogg', etc.)
        start_time: Start time for audio extraction (in seconds)
        end_time: End time for audio extraction (in seconds)
        bitrate: Bitrate for the output audio file
        output_dir: Directory to save the exported audio file
    
    Returns:
        Path to the exported audio file
    
    Raises:
        InvalidFormatError: If the requested format is not supported
        ExportError: If the audio extraction fails
    """
    # Validate input file
    if not os.path.exists(video_path):
        raise ExportError(f"Video file not found: {video_path}")
    
    # Validate format
    supported_formats = ['mp3', 'aac', 'wav', 'ogg', 'flac']
    if fmt.lower() not in supported_formats:
        raise InvalidFormatError(f"Unsupported audio format: {fmt}. Supported formats: {', '.join(supported_formats)}")
    
    # Validate time range if provided
    video_duration = get_video_duration(video_path)
    if start_time is not None and start_time < 0:
        raise ExportError("Start time cannot be negative")
    
    if end_time is not None and end_time > video_duration:
        raise ExportError(f"End time exceeds video duration ({video_duration} seconds)")
    
    if start_time is not None and end_time is not None and start_time >= end_time:
        raise ExportError("Start time must be before end time")
    
    try:
        # Extract audio from video
        audio_path = extract_audio(
            video_path, 
            fmt, 
            start_time=start_time, 
            end_time=end_time,
            bitrate=bitrate,
            output_dir=output_dir
        )
        
        # Normalize audio levels
        normalized_path = normalize_audio(audio_path)
        
        # Preserve metadata from original video
        metadata = get_video_metadata(video_path)
        if metadata:
            final_path = add_audio_metadata(normalized_path, metadata)
        else:
            final_path = normalized_path
        
        return final_path
        
    except Exception as e:
        # Clean up partially extracted files
        if 'audio_path' in locals() and os.path.exists(audio_path):
            os.remove(audio_path)
        if 'normalized_path' in locals() and os.path.exists(normalized_path):
            os.remove(normalized_path)
        raise ExportError(f"Failed to export audio: {str(e)}")

# Alias for backward compatibility
def export_audio(video_path: str, fmt: str = 'mp3') -> str:
    """Backward compatibility alias for export_audio_only."""
    return export_audio_only(video_path, fmt)
