#!/usr/bin/env python3
"""
Test yt-dlp search functionality to debug search issues
"""

import sys
import os
sys.path.append('VideoApp')

import yt_dlp

def test_search():
    """Test basic yt-dlp search"""
    ydl_opts = {
        'quiet': False,
        'no_warnings': False,
        'extract_flat': True,
        'ignoreerrors': True,
        'no_check_certificate': True,
        'socket_timeout': 30,
        'retries': 3,
    }

    try:
        search_query = 'ytsearch5:python tutorial'
        print(f'Testing search query: {search_query}')
        
        with yt_dlp.YoutubeDL(ydl_opts) as ydl:
            search_results = ydl.extract_info(search_query, download=False)
        
        if search_results and 'entries' in search_results:
            print(f'Found {len(search_results["entries"])} entries')
            for i, entry in enumerate(search_results['entries'][:3]):
                if entry:
                    print(f'{i+1}. {entry.get("title", "No title")} - {entry.get("id", "No ID")}')
                    print(f'   URL: {entry.get("url", "No URL")}')
                    print(f'   Duration: {entry.get("duration", "Unknown")}')
                    print(f'   Views: {entry.get("view_count", "Unknown")}')
                    print()
        else:
            print('No entries found')
            print(f'Search results type: {type(search_results)}')
            if search_results:
                print(f'Search results keys: {search_results.keys()}')
                
    except Exception as e:
        print(f'Error: {e}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_search()
