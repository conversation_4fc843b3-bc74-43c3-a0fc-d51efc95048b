# -*- coding: utf-8 -*-
# Audio management module for VideoApp

import os
import tempfile
from typing import List, Optional, Dict, Any
from dataclasses import dataclass
from utils.audio_utils import (
    merge_audio_files, 
    adjust_audio_speed, 
    change_audio_pitch,
    apply_audio_filters,
    convert_audio_format
)
from utils.exceptions import AudioProcessingError

@dataclass
class AudioClip:
    """Represents an audio clip with metadata."""
    path: str
    duration: float
    format: str
    bitrate: int
    sample_rate: int
    channels: int
    metadata: Optional[Dict[str, Any]] = None

class AudioManager:
    """Manages audio files for the video editing application."""
    
    def __init__(self, working_dir: Optional[str] = None):
        self.working_dir = working_dir or tempfile.mkdtemp(prefix="videoapp_audio_")
        self.audio_clips: List[AudioClip] = []
    
    def import_audio(self, audio_path: str) -> AudioClip:
        """Import an audio file into the manager."""
        if not os.path.exists(audio_path):
            raise AudioProcessingError(f"Audio file not found: {audio_path}")
        
        # Extract audio metadata
        from utils.audio_utils import get_audio_metadata
        metadata = get_audio_metadata(audio_path)
        
        clip = AudioClip(
            path=audio_path,
            duration=metadata.get('duration', 0),
            format=metadata.get('format', 'unknown'),
            bitrate=metadata.get('bitrate', 0),
            sample_rate=metadata.get('sample_rate', 0),
            channels=metadata.get('channels', 0),
            metadata=metadata
        )
        
        self.audio_clips.append(clip)
        return clip
    
    def create_audio_mix(self, clip_paths: List[str], output_format: str = 'mp3') -> str:
        """Create a mix from multiple audio clips."""
        if not clip_paths:
            raise AudioProcessingError("No audio clips provided for mixing")
        
        # Import all clips
        clips = [self.import_audio(path) for path in clip_paths]
        
        # Merge audio files
        output_path = os.path.join(self.working_dir, f"audio_mix.{output_format}")
        merged_path = merge_audio_files([clip.path for clip in clips], output_path)
        
        return merged_path
    
    def apply_audio_effect(self, audio_path: str, effect_type: str, **kwargs) -> str:
        """Apply an effect to an audio file."""
        supported_effects = ['speed', 'pitch', 'fade', 'reverb', 'equalizer']
        
        if effect_type not in supported_effects:
            raise AudioProcessingError(f"Unsupported effect: {effect_type}. Supported: {', '.join(supported_effects)}")
        
        # Generate output path
        base_name = os.path.splitext(os.path.basename(audio_path))[0]
        output_path = os.path.join(self.working_dir, f"{base_name}_{effect_type}.mp3")
        
        try:
            if effect_type == 'speed':
                factor = kwargs.get('factor', 1.0)
                return adjust_audio_speed(audio_path, output_path, factor)
            elif effect_type == 'pitch':
                semitones = kwargs.get('semitones', 0)
                return change_audio_pitch(audio_path, output_path, semitones)
            elif effect_type == 'fade':
                fade_in = kwargs.get('fade_in', 0)
                fade_out = kwargs.get('fade_out', 0)
                return apply_audio_filters(audio_path, output_path, f"afade=t=in:st=0:d={fade_in},afade=t=out:st={kwargs.get('duration', 10)-fade_out}:d={fade_out}")
            else:
                # For other effects, use generic filter application
                return apply_audio_filters(audio_path, output_path, kwargs.get('filter_string', ''))
        except Exception as e:
            raise AudioProcessingError(f"Failed to apply {effect_type} effect: {str(e)}")
    
    def convert_audio(self, audio_path: str, target_format: str, bitrate: Optional[int] = None) -> str:
        """Convert audio file to different format."""
        base_name = os.path.splitext(os.path.basename(audio_path))[0]
        output_path = os.path.join(self.working_dir, f"{base_name}.{target_format}")
        
        return convert_audio_format(audio_path, output_path, target_format, bitrate)
    
    def cleanup(self):
        """Clean up temporary audio files."""
        import shutil
        if os.path.exists(self.working_dir):
            shutil.rmtree(self.working_dir)
