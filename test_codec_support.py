#!/usr/bin/env python3
"""
Test codec and multimedia support
"""

import sys
import os
sys.path.append('VideoApp')

from PyQt5.QtWidgets import QApplication
from PyQt5.QtMultimedia import QMediaPlayer, QMediaContent
from PyQt5.QtCore import QUrl

def test_codec_support():
    """Test what codecs and formats are supported"""
    app = QApplication.instance()
    if app is None:
        app = QApplication([])
    
    print("🧪 Testing Codec and Multimedia Support")
    print("=" * 50)
    
    # Test 1: Check if QMediaPlayer can be created
    try:
        player = QMediaPlayer()
        print("✅ QMediaPlayer created successfully")
        
        # Check player capabilities
        print(f"📊 Player state: {player.state()}")
        print(f"📊 Media status: {player.mediaStatus()}")
        print(f"📊 Error: {player.error()}")
        print(f"📊 Error string: {player.errorString()}")
        
    except Exception as e:
        print(f"❌ Failed to create QMediaPlayer: {e}")
        return
    
    # Test 2: Check supported formats
    print("\n🔍 Testing Format Support:")
    print("-" * 30)
    
    # Test with different URL formats
    test_urls = [
        "file:///C:/Windows/Media/Alarm01.wav",  # System sound file
        "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4",  # Online MP4
        "https://www.soundjay.com/misc/sounds/bell-ringing-05.wav",  # Online WAV
    ]
    
    for url in test_urls:
        print(f"\n🧪 Testing URL: {url}")
        try:
            media_content = QMediaContent(QUrl(url))
            player.setMedia(media_content)
            
            # Wait a moment for the media to be processed
            app.processEvents()
            
            print(f"   📊 Media status: {player.mediaStatus()}")
            print(f"   📊 Error: {player.error()}")
            if player.error() != 0:
                print(f"   ❌ Error string: {player.errorString()}")
            else:
                print(f"   ✅ Media loaded successfully")
                
        except Exception as e:
            print(f"   ❌ Exception: {e}")
    
    # Test 3: Check for common system files
    print("\n🔍 Testing Local System Files:")
    print("-" * 30)
    
    system_files = [
        "C:/Windows/Media/Alarm01.wav",
        "C:/Windows/Media/chimes.wav",
        "C:/Windows/Media/ding.wav",
    ]
    
    for file_path in system_files:
        if os.path.exists(file_path):
            print(f"\n🧪 Testing file: {file_path}")
            try:
                media_content = QMediaContent(QUrl.fromLocalFile(file_path))
                player.setMedia(media_content)
                
                app.processEvents()
                
                print(f"   📊 Media status: {player.mediaStatus()}")
                print(f"   📊 Error: {player.error()}")
                if player.error() != 0:
                    print(f"   ❌ Error string: {player.errorString()}")
                else:
                    print(f"   ✅ File loaded successfully")
                    
            except Exception as e:
                print(f"   ❌ Exception: {e}")
        else:
            print(f"❌ File not found: {file_path}")
    
    # Test 4: Check Qt Multimedia installation
    print("\n🔍 Checking Qt Multimedia Components:")
    print("-" * 30)
    
    try:
        from PyQt5.QtMultimedia import QMediaPlayer, QMediaContent, QAudioOutput
        print("✅ QMediaPlayer available")
        print("✅ QMediaContent available")
        print("✅ QAudioOutput available")
    except ImportError as e:
        print(f"❌ Import error: {e}")
    
    try:
        from PyQt5.QtMultimediaWidgets import QVideoWidget
        print("✅ QVideoWidget available")
    except ImportError as e:
        print(f"❌ QVideoWidget not available: {e}")
    
    # Test 5: Check for codec libraries
    print("\n🔍 Checking Codec Libraries:")
    print("-" * 30)
    
    codec_checks = [
        ("FFmpeg", "ffmpeg"),
        ("GStreamer", "gst-launch-1.0"),
        ("DirectShow", None),  # Windows only
    ]
    
    import shutil
    for name, command in codec_checks:
        if command:
            if shutil.which(command):
                print(f"✅ {name} found")
            else:
                print(f"❌ {name} not found")
        else:
            print(f"ℹ️ {name} (Windows DirectShow - built-in)")
    
    print("\n" + "=" * 50)
    print("🏁 Codec Support Test Complete")
    
    # Recommendations
    print("\n💡 Recommendations:")
    if player.error() != 0:
        print("- Install Qt multimedia codecs")
        print("- Install FFmpeg")
        print("- Check Windows Media Feature Pack")
        print("- Try installing K-Lite Codec Pack")
    else:
        print("- Basic multimedia support appears to be working")

if __name__ == "__main__":
    test_codec_support()
