# -*- coding: utf-8 -*-
"""
Compatibility module for pyaudioop which was removed in Python 3.13
This provides basic functionality to prevent pydub from failing
"""

def mul(fragment, width, factor):
    """Multiply audio fragment by factor"""
    # Simple implementation - just return the fragment
    # In a real implementation, this would multiply the audio samples
    return fragment

def add(fragment1, fragment2, width):
    """Add two audio fragments"""
    # Simple implementation - just return the first fragment
    # In a real implementation, this would add the audio samples
    return fragment1

def bias(fragment, width, bias):
    """Add bias to audio fragment"""
    # Simple implementation - just return the fragment
    return fragment

def reverse(fragment, width):
    """Reverse audio fragment"""
    # Simple implementation - just return the fragment
    return fragment

def lin2lin(fragment, width, newwidth):
    """Convert between different sample widths"""
    # Simple implementation - just return the fragment
    return fragment

def ratecv(fragment, width, nchannels, inrate, outrate, state, weightA=1, weightB=0):
    """Convert sample rate"""
    # Simple implementation - just return the fragment and None state
    return fragment, None

def tomono(fragment, width, lfactor, rfactor):
    """Convert stereo to mono"""
    # Simple implementation - just return the fragment
    return fragment

def tostereo(fragment, width, lfactor, rfactor):
    """Convert mono to stereo"""
    # Simple implementation - just return the fragment doubled
    return fragment + fragment

def getsample(fragment, width, index):
    """Get a sample from fragment"""
    # Simple implementation - just return 0
    return 0

def max(fragment, width):
    """Get maximum sample value"""
    # Simple implementation - just return 0
    return 0

def minmax(fragment, width):
    """Get minimum and maximum sample values"""
    # Simple implementation - just return (0, 0)
    return (0, 0)

def avg(fragment, width):
    """Get average sample value"""
    # Simple implementation - just return 0
    return 0

def avgpp(fragment, width):
    """Get average peak-to-peak value"""
    # Simple implementation - just return 0
    return 0

def rms(fragment, width):
    """Get RMS value"""
    # Simple implementation - just return 0
    return 0

def cross(fragment, width):
    """Count zero crossings"""
    # Simple implementation - just return 0
    return 0

def findfactor(fragment, reference):
    """Find factor to match reference"""
    # Simple implementation - just return 1.0
    return 1.0

def findfit(fragment, reference):
    """Find best fit"""
    # Simple implementation - just return (0, 1.0)
    return (0, 1.0)

def findmax(fragment, length):
    """Find maximum correlation"""
    # Simple implementation - just return 0
    return 0

def getsample(fragment, width, index):
    """Get sample at index"""
    # Simple implementation - just return 0
    return 0

# Error class for compatibility
class error(Exception):
    """Audio operation error"""
    pass
