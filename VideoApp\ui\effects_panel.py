# -*- coding: utf-8 -*-
"""
Effects panel widget for video editing
"""

from core.libraries import *
from core.theme import *
from editing.color_selection import COLORS, select_color
from editing.font_selection import list_fonts


class EffectsPanel(QWidget):
    """Panel containing various video effects and controls"""
    
    # Signals
    effect_applied = pyqtSignal(str, dict)  # effect_type, parameters
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        self.connect_signals()
    
    def setup_ui(self):
        """Setup the effects panel UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        
        # Title
        title_label = QLabel("Effects Panel")
        title_label.setStyleSheet(f"""
            QLabel {{
                color: {ACCENT_COLOR};
                font-weight: bold;
                font-size: 16px;
                margin-bottom: 10px;
            }}
        """)
        
        # Create tabs for different effect categories
        self.tab_widget = QTabWidget()
        self.tab_widget.setStyleSheet(f"""
            QTabWidget::pane {{
                border: 1px solid {PANEL_COLOR};
                background-color: {BACKGROUND_COLOR};
            }}
            QTabBar::tab {{
                background-color: {PANEL_COLOR};
                color: {TEXT_COLOR};
                padding: 8px 16px;
                margin-right: 2px;
            }}
            QTabBar::tab:selected {{
                background-color: {ACCENT_COLOR};
                color: {BACKGROUND_COLOR};
            }}
            QTabBar::tab:hover {{
                background-color: {TEXT_COLOR};
                color: {BACKGROUND_COLOR};
            }}
        """)
        
        # Text Effects Tab
        self.text_tab = self.create_text_effects_tab()
        self.tab_widget.addTab(self.text_tab, "Text")
        
        # Image Effects Tab
        self.image_tab = self.create_image_effects_tab()
        self.tab_widget.addTab(self.image_tab, "Images")
        
        # Video Effects Tab
        self.video_tab = self.create_video_effects_tab()
        self.tab_widget.addTab(self.video_tab, "Video")
        
        # Audio Effects Tab
        self.audio_tab = self.create_audio_effects_tab()
        self.tab_widget.addTab(self.audio_tab, "Audio")
        
        # Add to main layout
        layout.addWidget(title_label)
        layout.addWidget(self.tab_widget)
        
        # Apply overall styling
        self.setStyleSheet(f"""
            QWidget {{
                background-color: {PANEL_COLOR};
                border: 2px solid {ACCENT_COLOR};
                border-radius: 10px;
            }}
        """)
    
    def create_text_effects_tab(self):
        """Create text effects tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Text input
        text_group = QGroupBox("Add Text")
        text_layout = QVBoxLayout(text_group)
        
        self.text_input = QLineEdit()
        self.text_input.setPlaceholderText("Enter text to add...")
        self.text_input.setStyleSheet(f"""
            QLineEdit {{
                background-color: {BACKGROUND_COLOR};
                color: {TEXT_COLOR};
                border: 1px solid {PANEL_COLOR};
                border-radius: 3px;
                padding: 5px;
            }}
        """)
        
        # Font selection
        font_layout = QHBoxLayout()
        font_layout.addWidget(QLabel("Font:"))
        
        self.font_combo = QComboBox()
        self.font_combo.addItems(list_fonts()[:20])  # Limit to first 20 fonts
        self.font_combo.setStyleSheet(f"""
            QComboBox {{
                background-color: {BACKGROUND_COLOR};
                color: {TEXT_COLOR};
                border: 1px solid {PANEL_COLOR};
                border-radius: 3px;
                padding: 3px;
            }}
        """)
        font_layout.addWidget(self.font_combo)
        
        # Font size
        self.font_size = QSpinBox()
        self.font_size.setRange(8, 72)
        self.font_size.setValue(24)
        self.font_size.setStyleSheet(f"""
            QSpinBox {{
                background-color: {BACKGROUND_COLOR};
                color: {TEXT_COLOR};
                border: 1px solid {PANEL_COLOR};
                border-radius: 3px;
                padding: 3px;
            }}
        """)
        font_layout.addWidget(QLabel("Size:"))
        font_layout.addWidget(self.font_size)
        
        # Color selection
        color_layout = QHBoxLayout()
        self.text_color_btn = QPushButton("Text Color")
        self.text_color_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {ACCENT_COLOR};
                color: {BACKGROUND_COLOR};
                border: none;
                border-radius: 3px;
                padding: 5px 10px;
            }}
        """)
        color_layout.addWidget(self.text_color_btn)
        
        # Position selection
        position_layout = QHBoxLayout()
        position_layout.addWidget(QLabel("Position:"))
        
        self.position_combo = QComboBox()
        self.position_combo.addItems([
            "center", "top", "bottom", "left", "right",
            "top-left", "top-right", "bottom-left", "bottom-right"
        ])
        self.position_combo.setStyleSheet(f"""
            QComboBox {{
                background-color: {BACKGROUND_COLOR};
                color: {TEXT_COLOR};
                border: 1px solid {PANEL_COLOR};
                border-radius: 3px;
                padding: 3px;
            }}
        """)
        position_layout.addWidget(self.position_combo)
        
        # Timing controls
        timing_layout = QHBoxLayout()
        
        self.start_time = QDoubleSpinBox()
        self.start_time.setRange(0, 3600)
        self.start_time.setValue(0)
        self.start_time.setSuffix(" s")
        
        self.duration = QDoubleSpinBox()
        self.duration.setRange(0.1, 3600)
        self.duration.setValue(5.0)
        self.duration.setSuffix(" s")
        
        timing_layout.addWidget(QLabel("Start:"))
        timing_layout.addWidget(self.start_time)
        timing_layout.addWidget(QLabel("Duration:"))
        timing_layout.addWidget(self.duration)
        
        # Apply button
        self.apply_text_btn = QPushButton("Add Text")
        self.apply_text_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {ACCENT_COLOR};
                color: {BACKGROUND_COLOR};
                border: none;
                border-radius: 5px;
                padding: 8px 16px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: {TEXT_COLOR};
            }}
        """)
        
        # Add to layout
        text_layout.addWidget(self.text_input)
        text_layout.addLayout(font_layout)
        text_layout.addLayout(color_layout)
        text_layout.addLayout(position_layout)
        text_layout.addLayout(timing_layout)
        text_layout.addWidget(self.apply_text_btn)
        
        layout.addWidget(text_group)
        layout.addStretch()
        
        return tab
    
    def create_image_effects_tab(self):
        """Create image effects tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Image overlay group
        image_group = QGroupBox("Add Image Overlay")
        image_layout = QVBoxLayout(image_group)
        
        # Image file selection
        file_layout = QHBoxLayout()
        self.image_path = QLineEdit()
        self.image_path.setPlaceholderText("Select image file...")
        self.image_path.setStyleSheet(f"""
            QLineEdit {{
                background-color: {BACKGROUND_COLOR};
                color: {TEXT_COLOR};
                border: 1px solid {PANEL_COLOR};
                border-radius: 3px;
                padding: 5px;
            }}
        """)
        
        self.browse_image_btn = QPushButton("Browse")
        self.browse_image_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {PANEL_COLOR};
                color: {TEXT_COLOR};
                border: 1px solid {ACCENT_COLOR};
                border-radius: 3px;
                padding: 5px 10px;
            }}
            QPushButton:hover {{
                background-color: {ACCENT_COLOR};
                color: {BACKGROUND_COLOR};
            }}
        """)
        
        file_layout.addWidget(self.image_path)
        file_layout.addWidget(self.browse_image_btn)
        
        # Position and size
        pos_size_layout = QHBoxLayout()
        
        self.image_position = QComboBox()
        self.image_position.addItems([
            "center", "top-left", "top-right", "bottom-left", "bottom-right"
        ])
        
        self.image_scale = QDoubleSpinBox()
        self.image_scale.setRange(0.1, 5.0)
        self.image_scale.setValue(1.0)
        self.image_scale.setSingleStep(0.1)
        
        pos_size_layout.addWidget(QLabel("Position:"))
        pos_size_layout.addWidget(self.image_position)
        pos_size_layout.addWidget(QLabel("Scale:"))
        pos_size_layout.addWidget(self.image_scale)
        
        # Timing
        image_timing_layout = QHBoxLayout()
        
        self.image_start_time = QDoubleSpinBox()
        self.image_start_time.setRange(0, 3600)
        self.image_start_time.setValue(0)
        self.image_start_time.setSuffix(" s")
        
        self.image_duration = QDoubleSpinBox()
        self.image_duration.setRange(0.1, 3600)
        self.image_duration.setValue(5.0)
        self.image_duration.setSuffix(" s")
        
        image_timing_layout.addWidget(QLabel("Start:"))
        image_timing_layout.addWidget(self.image_start_time)
        image_timing_layout.addWidget(QLabel("Duration:"))
        image_timing_layout.addWidget(self.image_duration)
        
        # Apply button
        self.apply_image_btn = QPushButton("Add Image")
        self.apply_image_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {ACCENT_COLOR};
                color: {BACKGROUND_COLOR};
                border: none;
                border-radius: 5px;
                padding: 8px 16px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: {TEXT_COLOR};
            }}
        """)
        
        # Add to layout
        image_layout.addLayout(file_layout)
        image_layout.addLayout(pos_size_layout)
        image_layout.addLayout(image_timing_layout)
        image_layout.addWidget(self.apply_image_btn)
        
        layout.addWidget(image_group)
        layout.addStretch()
        
        return tab
    
    def create_video_effects_tab(self):
        """Create video effects tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Adjustments group
        adj_group = QGroupBox("Video Adjustments")
        adj_layout = QVBoxLayout(adj_group)
        
        # Brightness
        brightness_layout = QHBoxLayout()
        brightness_layout.addWidget(QLabel("Brightness:"))
        self.brightness_slider = QSlider(Qt.Horizontal)
        self.brightness_slider.setRange(-100, 100)
        self.brightness_slider.setValue(0)
        self.brightness_label = QLabel("0")
        brightness_layout.addWidget(self.brightness_slider)
        brightness_layout.addWidget(self.brightness_label)
        
        # Contrast
        contrast_layout = QHBoxLayout()
        contrast_layout.addWidget(QLabel("Contrast:"))
        self.contrast_slider = QSlider(Qt.Horizontal)
        self.contrast_slider.setRange(50, 200)
        self.contrast_slider.setValue(100)
        self.contrast_label = QLabel("100")
        contrast_layout.addWidget(self.contrast_slider)
        contrast_layout.addWidget(self.contrast_label)
        
        # Saturation
        saturation_layout = QHBoxLayout()
        saturation_layout.addWidget(QLabel("Saturation:"))
        self.saturation_slider = QSlider(Qt.Horizontal)
        self.saturation_slider.setRange(0, 200)
        self.saturation_slider.setValue(100)
        self.saturation_label = QLabel("100")
        saturation_layout.addWidget(self.saturation_slider)
        saturation_layout.addWidget(self.saturation_label)
        
        # Apply adjustments button
        self.apply_adjustments_btn = QPushButton("Apply Adjustments")
        self.apply_adjustments_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {ACCENT_COLOR};
                color: {BACKGROUND_COLOR};
                border: none;
                border-radius: 5px;
                padding: 8px 16px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: {TEXT_COLOR};
            }}
        """)
        
        adj_layout.addLayout(brightness_layout)
        adj_layout.addLayout(contrast_layout)
        adj_layout.addLayout(saturation_layout)
        adj_layout.addWidget(self.apply_adjustments_btn)
        
        # Effects group
        effects_group = QGroupBox("Special Effects")
        effects_layout = QVBoxLayout(effects_group)
        
        # Effect buttons
        self.blur_btn = QPushButton("Blur Effect")
        self.mirror_btn = QPushButton("Mirror Effect")
        self.grayscale_btn = QPushButton("Grayscale")
        self.invert_btn = QPushButton("Invert Colors")
        
        for btn in [self.blur_btn, self.mirror_btn, self.grayscale_btn, self.invert_btn]:
            btn.setStyleSheet(f"""
                QPushButton {{
                    background-color: {PANEL_COLOR};
                    color: {TEXT_COLOR};
                    border: 1px solid {ACCENT_COLOR};
                    border-radius: 3px;
                    padding: 8px;
                    margin: 2px;
                }}
                QPushButton:hover {{
                    background-color: {ACCENT_COLOR};
                    color: {BACKGROUND_COLOR};
                }}
            """)
        
        effects_layout.addWidget(self.blur_btn)
        effects_layout.addWidget(self.mirror_btn)
        effects_layout.addWidget(self.grayscale_btn)
        effects_layout.addWidget(self.invert_btn)
        
        layout.addWidget(adj_group)
        layout.addWidget(effects_group)
        layout.addStretch()
        
        return tab
    
    def create_audio_effects_tab(self):
        """Create audio effects tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Volume group
        volume_group = QGroupBox("Audio Controls")
        volume_layout = QVBoxLayout(volume_group)
        
        # Volume slider
        vol_layout = QHBoxLayout()
        vol_layout.addWidget(QLabel("Volume:"))
        self.volume_slider = QSlider(Qt.Horizontal)
        self.volume_slider.setRange(0, 200)
        self.volume_slider.setValue(100)
        self.volume_label = QLabel("100%")
        vol_layout.addWidget(self.volume_slider)
        vol_layout.addWidget(self.volume_label)
        
        # Audio effects buttons
        self.fade_in_btn = QPushButton("Fade In")
        self.fade_out_btn = QPushButton("Fade Out")
        self.normalize_btn = QPushButton("Normalize Audio")
        
        for btn in [self.fade_in_btn, self.fade_out_btn, self.normalize_btn]:
            btn.setStyleSheet(f"""
                QPushButton {{
                    background-color: {PANEL_COLOR};
                    color: {TEXT_COLOR};
                    border: 1px solid {ACCENT_COLOR};
                    border-radius: 3px;
                    padding: 8px;
                    margin: 2px;
                }}
                QPushButton:hover {{
                    background-color: {ACCENT_COLOR};
                    color: {BACKGROUND_COLOR};
                }}
            """)
        
        volume_layout.addLayout(vol_layout)
        volume_layout.addWidget(self.fade_in_btn)
        volume_layout.addWidget(self.fade_out_btn)
        volume_layout.addWidget(self.normalize_btn)
        
        layout.addWidget(volume_group)
        layout.addStretch()
        
        return tab
    
    def connect_signals(self):
        """Connect widget signals"""
        # Text effects
        self.apply_text_btn.clicked.connect(self.apply_text_effect)
        self.text_color_btn.clicked.connect(self.select_text_color)
        
        # Image effects
        self.apply_image_btn.clicked.connect(self.apply_image_effect)
        self.browse_image_btn.clicked.connect(self.browse_image_file)
        
        # Video effects
        self.apply_adjustments_btn.clicked.connect(self.apply_video_adjustments)
        self.blur_btn.clicked.connect(lambda: self.apply_special_effect("blur"))
        self.mirror_btn.clicked.connect(lambda: self.apply_special_effect("mirror"))
        self.grayscale_btn.clicked.connect(lambda: self.apply_special_effect("grayscale"))
        self.invert_btn.clicked.connect(lambda: self.apply_special_effect("invert"))
        
        # Slider updates
        self.brightness_slider.valueChanged.connect(lambda v: self.brightness_label.setText(str(v)))
        self.contrast_slider.valueChanged.connect(lambda v: self.contrast_label.setText(str(v)))
        self.saturation_slider.valueChanged.connect(lambda v: self.saturation_label.setText(str(v)))
        self.volume_slider.valueChanged.connect(lambda v: self.volume_label.setText(f"{v}%"))
    
    def apply_text_effect(self):
        """Apply text effect"""
        parameters = {
            'text': self.text_input.text(),
            'font_family': self.font_combo.currentText(),
            'font_size': self.font_size.value(),
            'color': getattr(self, 'selected_text_color', '#FFFFFF'),
            'position': self.position_combo.currentText(),
            'start_time': self.start_time.value(),
            'duration': self.duration.value()
        }
        self.effect_applied.emit("text", parameters)
    
    def apply_image_effect(self):
        """Apply image effect"""
        parameters = {
            'image_path': self.image_path.text(),
            'position': self.image_position.currentText(),
            'size': (int(100 * self.image_scale.value()), None),
            'start_time': self.image_start_time.value(),
            'duration': self.image_duration.value()
        }
        self.effect_applied.emit("image", parameters)
    
    def apply_video_adjustments(self):
        """Apply video adjustments"""
        parameters = {
            'brightness': self.brightness_slider.value() / 100.0,
            'contrast': self.contrast_slider.value() / 100.0,
            'saturation': self.saturation_slider.value() / 100.0
        }
        self.effect_applied.emit("adjustment", parameters)
    
    def apply_special_effect(self, effect_type):
        """Apply special effect"""
        parameters = {}
        if effect_type == "blur":
            parameters = {'radius': 2}
        elif effect_type == "mirror":
            parameters = {'direction': 'horizontal'}
        
        self.effect_applied.emit(effect_type, parameters)
    
    def select_text_color(self):
        """Open color selection dialog"""
        color = select_color("#FFFFFF", self)
        if color:
            self.selected_text_color = color
            self.text_color_btn.setStyleSheet(f"""
                QPushButton {{
                    background-color: {color};
                    color: {'#000000' if color == '#FFFFFF' else '#FFFFFF'};
                    border: none;
                    border-radius: 3px;
                    padding: 5px 10px;
                }}
            """)
    
    def browse_image_file(self):
        """Browse for image file"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, 
            "Select Image File", 
            "", 
            "Image Files (*.png *.jpg *.jpeg *.bmp *.gif)"
        )
        if file_path:
            self.image_path.setText(file_path)
