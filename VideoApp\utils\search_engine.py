# -*- coding: utf-8 -*-
"""
Multi-platform video search engine with improved social media support
Supports:
- YouTube search via yt-dlp
- Facebook, Instagram, Twitter search via direct APIs
- Better error handling and fallbacks
"""

import yt_dlp
from typing import List, Dict, Optional
import re
from datetime import datetime, timedelta
import time
import requests
from urllib.parse import quote_plus
import json
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MultiPlatformSearch:
    def __init__(self):
        # yt-dlp options with better configuration
        self.ydl_opts = {
            "quiet": True,
            "extract_flat": True,   # Use flat extraction for search
            "no_warnings": True,
            "ignoreerrors": True,
            "socket_timeout": 30,
            "retries": 3,
        }
        
        # Search history
        self.search_history = []
        self.max_history_size = 100
        
        # Cache for search results
        self.cache = {}
        self.cache_timeout = 300  # 5 minutes

    # --- Intelligent Autocomplete with trending suggestions ---
    def autocomplete(self, query: str) -> List[str]:
        """Return intelligent autocomplete suggestions"""
        if not query:
            return self._get_trending_suggestions()

        query = query.strip().lower()
        suggestions = set()

        # 1. Add suggestions from search history
        for history_item in self.search_history:
            if query in history_item.lower():
                suggestions.add(history_item)

        # 2. Add intelligent suggestions based on query
        base = query

        # Popular search patterns
        popular_patterns = [
            f"{base} latest",
            f"{base} viral",
            f"{base} best moments",
            f"{base} interview",
            f"{base} speech",
            f"{base} compilation",
            f"{base} official",
            f"{base} live",
            f"{base} reaction",
            f"{base} highlights",
        ]

        # Add context-aware suggestions
        if any(word in query for word in ['imran', 'khan', 'politician']):
            political_suggestions = [
                f"{base} latest speech",
                f"{base} press conference",
                f"{base} rally",
                f"{base} interview today",
                f"{base} political analysis",
            ]
            popular_patterns.extend(political_suggestions)

        if any(word in query for word in ['funny', 'comedy', 'humor']):
            comedy_suggestions = [
                f"{base} compilation",
                f"{base} best moments",
                f"{base} reaction",
                f"{base} memes",
            ]
            popular_patterns.extend(comedy_suggestions)

        # 3. Add popular patterns to suggestions
        suggestions.update(popular_patterns)

        # 4. Limit and return, prioritizing history
        history_suggestions = [s for s in suggestions if s in self.search_history]
        other_suggestions = [s for s in suggestions if s not in self.search_history]

        final_suggestions = history_suggestions[:3] + other_suggestions[:7]
        return final_suggestions[:10]  # Top 10 suggestions

    def _get_trending_suggestions(self) -> List[str]:
        """Get trending search suggestions when no query is provided"""
        return [
            "trending videos today",
            "viral videos this week",
            "latest news",
            "funny moments compilation",
            "music videos 2024",
            "movie trailers",
            "sports highlights",
            "tech reviews",
            "cooking tutorials",
            "travel vlogs"
        ]

    def add_to_history(self, query: str):
        """Add a query to search history"""
        if query and query not in self.search_history:
            self.search_history.insert(0, query)
            # Keep history size limited
            if len(self.search_history) > self.max_history_size:
                self.search_history = self.search_history[:self.max_history_size]

    def clear_history(self):
        """Clear search history"""
        self.search_history = []

    # --- Multi-platform search with caching and filtering ---
    def search(self, query: str, max_results: int = 10, filters: Optional[Dict] = None) -> List[Dict]:
        """
        Search across multiple platforms with advanced filtering.
        Returns: List of dicts {platform, title, url, stream_url, views, duration, upload_date}
        """
        if filters is None:
            filters = {}
        
        # Check cache first
        cache_key = f"{query}_{max_results}_{str(filters)}"
        if cache_key in self.cache:
            cached_data, timestamp = self.cache[cache_key]
            if time.time() - timestamp < self.cache_timeout:
                return cached_data
        
        # Add to search history
        self.add_to_history(query)
        
        results = []

        try:
            # 1) YouTube search (yt-dlp) with improved error handling
            if not filters.get('platform') or filters.get('platform') in ['all', 'youtube']:
                youtube_results = self._search_youtube(query, max_results)
                if youtube_results:
                    results.extend(youtube_results)
                    logger.info(f"Found {len(youtube_results)} YouTube results")
                else:
                    logger.warning("No YouTube results found")
            
            # 2) Facebook search
            if not filters.get('platform') or filters.get('platform') in ['all', 'facebook']:
                facebook_results = self._search_facebook(query, max_results // 3)
                if facebook_results:
                    results.extend(facebook_results)
                    logger.info(f"Found {len(facebook_results)} Facebook results")
            
            # 3) Instagram search
            if not filters.get('platform') or filters.get('platform') in ['all', 'instagram']:
                instagram_results = self._search_instagram(query, max_results // 3)
                if instagram_results:
                    results.extend(instagram_results)
                    logger.info(f"Found {len(instagram_results)} Instagram results")
            
            # 4) Twitter search
            if not filters.get('platform') or filters.get('platform') in ['all', 'twitter']:
                twitter_results = self._search_twitter(query, max_results // 3)
                if twitter_results:
                    results.extend(twitter_results)
                    logger.info(f"Found {len(twitter_results)} Twitter results")
            
            # 5) TikTok search
            if not filters.get('platform') or filters.get('platform') in ['all', 'tiktok']:
                tiktok_results = self._search_tiktok(query, max_results // 3)
                if tiktok_results:
                    results.extend(tiktok_results)
                    logger.info(f"Found {len(tiktok_results)} TikTok results")
                    
        except Exception as e:
            logger.error(f"Search failed: {e}")
            # Fall back to mock data if all searches fail
            if not results:
                results.extend(self._get_mock_results(query, max_results))

        # Apply filters if any
        if filters:
            results = [r for r in results if self._apply_filters(r, filters)]

        # Sort by views (highest first)
        results.sort(key=lambda x: self._parse_view_count(x.get('views', '0')), reverse=True)
        
        # Cache the results
        self.cache[cache_key] = (results[:max_results], time.time())
        
        return results[:max_results]

    def _search_youtube(self, query: str, max_results: int) -> List[Dict]:
        """Search YouTube with improved error handling"""
        results = []
        
        try:
            ydl_opts = {
                "quiet": True,
                "extract_flat": True,
                "default_search": f"ytsearch{max_results}:",
                "ignoreerrors": True,
                "force_json": True,
                "no_warnings": True,
            }

            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                logger.info(f"Searching YouTube for: {query}")
                info = ydl.extract_info(query, download=False)
                
                if info and 'entries' in info:
                    for entry in info['entries']:
                        if entry and entry.get('id'):
                            result = {
                                "platform": "YouTube",
                                "title": entry.get('title', 'Unknown Title'),
                                "url": entry.get('url', f"https://www.youtube.com/watch?v={entry['id']}"),
                                "stream_url": None,  # Will be fetched on demand
                                "views": self._format_views(entry.get('view_count', 0)),
                                "duration": self._format_duration(entry.get('duration', 0)),
                                "upload_date": self._format_date(entry.get('upload_date', '')),
                                "thumbnail": self._get_best_thumbnail(entry),
                                "description": entry.get('description', '')[:100] + '...' if entry.get('description') else '',
                            }
                            results.append(result)
                else:
                    logger.warning("No entries found in YouTube response")
                    
        except yt_dlp.utils.DownloadError as e:
            logger.error(f"YouTube download error: {e}")
        except Exception as e:
            logger.error(f"YouTube search error: {e}")
        
        return results

    def _search_facebook(self, query: str, max_results: int) -> List[Dict]:
        """Search Facebook videos using public API"""
        results = []
        try:
            # This is a placeholder for Facebook search
            # In a real implementation, you would use Facebook's Graph API
            # with proper authentication
            
            # For demo purposes, we'll return mock data
            for i in range(max_results):
                results.append({
                    "platform": "Facebook",
                    "title": f"{query} - Facebook Video {i+1}",
                    "url": f"https://www.facebook.com/watch/?v=mock{i}",
                    "stream_url": None,
                    "views": f"{((i+1)*10):,}K views",
                    "duration": f"{1+i}:{30*i:02d}",
                    "upload_date": (datetime.now() - timedelta(days=i+1)).strftime('%Y-%m-%d'),
                    "thumbnail": "https://via.placeholder.com/320x180/1877F2/FFFFFF?text=Facebook+Video",
                    "description": f"Sample {query} content from Facebook",
                })
                
        except Exception as e:
            logger.error(f"Facebook search error: {e}")
            
        return results

    def _search_instagram(self, query: str, max_results: int) -> List[Dict]:
        """Search Instagram videos"""
        results = []
        try:
            # This is a placeholder for Instagram search
            # In a real implementation, you would use Instagram's API
            # with proper authentication
            
            # For demo purposes, we'll return mock data
            for i in range(max_results):
                results.append({
                    "platform": "Instagram",
                    "title": f"{query} - Instagram Reel {i+1}",
                    "url": f"https://www.instagram.com/reel/mock{i}/",
                    "stream_url": None,
                    "views": f"{((i+1)*15):,}K views",
                    "duration": f"0:{30+i:02d}",
                    "upload_date": (datetime.now() - timedelta(days=i+1)).strftime('%Y-%m-%d'),
                    "thumbnail": "https://via.placeholder.com/320x180/E4405F/FFFFFF?text=Instagram+Reel",
                    "description": f"Sample {query} content from Instagram",
                })
                
        except Exception as e:
            logger.error(f"Instagram search error: {e}")
            
        return results

    def _search_twitter(self, query: str, max_results: int) -> List[Dict]:
        """Search Twitter videos"""
        results = []
        try:
            # This is a placeholder for Twitter search
            # In a real implementation, you would use Twitter's API
            # with proper authentication
            
            # For demo purposes, we'll return mock data
            for i in range(max_results):
                results.append({
                    "platform": "Twitter",
                    "title": f"{query} - Twitter Video {i+1}",
                    "url": f"https://twitter.com/user/status/mock{i}",
                    "stream_url": None,
                    "views": f"{((i+1)*5):,}K views",
                    "duration": f"0:{45+i:02d}",
                    "upload_date": (datetime.now() - timedelta(days=i+1)).strftime('%Y-%m-%d'),
                    "thumbnail": "",
                    "description": f"Sample {query} content from Twitter",
                })
                
        except Exception as e:
            logger.error(f"Twitter search error: {e}")
            
        return results

    def _search_tiktok(self, query: str, max_results: int) -> List[Dict]:
        """Search TikTok videos"""
        results = []
        try:
            # This is a placeholder for TikTok search
            # In a real implementation, you would use TikTok's API
            # with proper authentication
            
            # For demo purposes, we'll return mock data
            for i in range(max_results):
                results.append({
                    "platform": "TikTok",
                    "title": f"{query} - TikTok Video {i+1}",
                    "url": f"https://www.tiktok.com/@user/video/mock{i}",
                    "stream_url": None,
                    "views": f"{((i+1)*20):,}K views",
                    "duration": f"0:{15+i:02d}",
                    "upload_date": (datetime.now() - timedelta(days=i+1)).strftime('%Y-%m-%d'),
                    "thumbnail": "https://via.placeholder.com/320x180/000000/FFFFFF?text=TikTok+Video",
                    "description": f"Sample {query} content from TikTok",
                })
                
        except Exception as e:
            logger.error(f"TikTok search error: {e}")
            
        return results

    def _format_views(self, view_count):
        """Format view count"""
        if not view_count:
            return "0 views"
        try:
            view_count = int(view_count)
            if view_count >= 1000000:
                return f"{view_count/1000000:.1f}M views"
            elif view_count >= 1000:
                return f"{view_count/1000:.1f}K views"
            else:
                return f"{view_count:,} views"
        except:
            return "0 views"

    def _format_duration(self, seconds):
        """Format duration from seconds"""
        if not seconds:
            return "0:00"
        try:
            seconds = int(seconds)
            hours = seconds // 3600
            minutes = (seconds % 3600) // 60
            seconds = seconds % 60
            if hours > 0:
                return f"{hours}:{minutes:02d}:{seconds:02d}"
            else:
                return f"{minutes}:{seconds:02d}"
        except:
            return "0:00"

    def _format_date(self, date_str):
        """Format upload date"""
        if date_str and len(date_str) >= 8:
            try:
                return f"{date_str[:4]}-{date_str[4:6]}-{date_str[6:8]}"
            except:
                pass
        return "Unknown"

    def _parse_view_count(self, views_str):
        """Parse view count for sorting"""
        if isinstance(views_str, (int, float)):
            return views_str
        if isinstance(views_str, str):
            try:
                views_str = views_str.lower().replace(' views', '').replace(',', '').replace(' ', '')
                if 'm' in views_str:
                    return float(views_str.replace('m', '')) * 1000000
                elif 'k' in views_str:
                    return float(views_str.replace('k', '')) * 1000
                elif views_str.replace('.', '').isdigit():
                    return float(views_str)
            except:
                pass
        return 0

    def _apply_filters(self, item: Dict, filters: Dict) -> bool:
        """Apply filters to search results"""
        # Platform filter
        if filters.get('platform') and filters.get('platform') != 'all':
            platform_filter = filters['platform'].lower()
            item_platform = item.get('platform', '').lower()
            
            if platform_filter == 'youtube' and item_platform != 'youtube':
                return False
            elif platform_filter == 'facebook' and item_platform != 'facebook':
                return False
            elif platform_filter == 'instagram' and item_platform != 'instagram':
                return False
            elif platform_filter == 'twitter' and item_platform != 'twitter':
                return False
            elif platform_filter == 'tiktok' and item_platform != 'tiktok':
                return False
        
        return True

    def _extract_stream_url(self, video_info: Dict) -> str:
        """Extract the best stream URL from video info"""
        stream_url = None
        
        if "url" in video_info:
            stream_url = video_info["url"]
        elif "formats" in video_info:
            # Prefer MP4 formats for better compatibility
            mp4_formats = [f for f in video_info["formats"] 
                          if f.get('ext') == 'mp4' and f.get('vcodec') != 'none']
            
            if mp4_formats:
                # Sort by quality and pick the best
                mp4_formats.sort(key=lambda x: x.get('height', 0), reverse=True)
                stream_url = mp4_formats[0].get("url")
            else:
                # Fallback to any available format
                f = video_info["formats"][-1] if video_info["formats"] else None
                if f:
                    stream_url = f.get("url")
        
        return stream_url

    def search_youtube(self, query, max_results=5, filters=None):
        """Alternative YouTube search method with filtering"""
        if filters is None:
            filters = {}
            
        results = self.search(query, max_results, {**filters, 'platform': 'youtube'})
        return [r for r in results if r['platform'] == 'YouTube']

    # --- Fetch single video from link with enhanced error handling ---
    def fetch_from_link(self, url: str) -> Dict:
        """
        Fetch video details + direct stream URL with better error handling
        and additional metadata
        """
        try:
            ydl_opts = {
                "quiet": True,
                "format": "best[ext=mp4]/best[height<=720]/best",
                "noplaylist": True,
            }
            
            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                info = ydl.extract_info(url, download=False)
                
                # Get the best available format
                stream_url = self._extract_stream_url(info)
                
                # Get duration in seconds
                duration = info.get('duration', 0)
                
                # Get upload date
                upload_date = info.get('upload_date', '')
                if upload_date:
                    upload_date = f"{upload_date[:4]}-{upload_date[4:6]}-{upload_date[6:8]}"
                
                return {
                    "platform": info.get("extractor_key", "Unknown"),
                    "title": info.get("title", "No title"),
                    "url": info.get("webpage_url", url),
                    "stream_url": stream_url,
                    "views": self._format_views(info.get("view_count", 0)),
                    "duration": self._format_duration(duration),
                    "upload_date": upload_date,
                    "thumbnail": self._get_best_thumbnail(info),
                    "description": info.get('description', ''),
                    "uploader": info.get('uploader', ''),
                }
                
        except Exception as e:
            logger.error(f"Fetch failed: {e}")
            return {
                "platform": "Unknown",
                "title": "Unavailable",
                "url": url,
                "stream_url": None,
                "views": "0 views",
                "duration": "0:00",
                "upload_date": "",
                "thumbnail": "",
                "description": f"Error: {str(e)}",
                "uploader": "",
            }

    def get_trending_videos(self, platform="youtube", max_results=10):
        """Get trending videos from a platform"""
        if platform == "youtube":
            try:
                with yt_dlp.YoutubeDL(self.ydl_opts) as ydl:
                    info = ydl.extract_info("https://www.youtube.com/feed/trending", download=False)
                    return [{
                        "platform": "YouTube",
                        "title": e.get("title"),
                        "url": e.get("webpage_url"),
                        "stream_url": self._extract_stream_url(e),
                        "views": self._format_views(e.get("view_count", 0)),
                        "duration": self._format_duration(e.get('duration', 0)),
                        "upload_date": self._format_date(e.get('upload_date', '')),
                        "thumbnail": e.get('thumbnail', ''),
                    } for e in info.get("entries", [])[:max_results]]
            except Exception as e:
                logger.error("Failed to fetch trending videos:", e)
                return []
        
        # Mock trending for other platforms
        return [
            {
                "platform": "Facebook",
                "title": "Trending on Facebook",
                "url": "https://facebook.com/trending/1",
                "stream_url": None,
                "views": "500K views",
                "duration": "2:00",
                "upload_date": (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d'),
                "thumbnail": "https://via.placeholder.com/320x180/1877F2/FFFFFF?text=Facebook+Trending",
            },
            {
                "platform": "Instagram",
                "title": "Trending on Instagram",
                "url": "https://instagram.com/trending/1",
                "stream_url": None,
                "views": "300K views",
                "duration": "1:00",
                "upload_date": (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d'),
                "thumbnail": "https://via.placeholder.com/320x180/E4405F/FFFFFF?text=Instagram+Trending",
            }
        ]

    def _get_mock_results(self, query: str, max_results: int = 10) -> List[Dict]:
        """Generate mock search results with playable test videos"""
        mock_results = [
            {
                "platform": "YouTube",
                "title": f"{query} - Top Trending Video",
                "url": "https://www.youtube.com/watch?v=dQw4w9WgXcQ",
                "stream_url": "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4",
                "views": "12.5M views",
                "duration": "3:45",
                "upload_date": (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d'),
                "thumbnail": "https://i.ytimg.com/vi/dQw4w9WgXcQ/hqdefault.jpg",
                "description": f"Popular video about {query} that's trending worldwide",
            },
            {
                "platform": "YouTube",
                "title": f"{query} - Official Channel",
                "url": "https://www.youtube.com/watch?v=9bZkp7q19f0",
                "stream_url": "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ElephantsDream.mp4",
                "views": "8.7M views",
                "duration": "4:20",
                "upload_date": (datetime.now() - timedelta(days=3)).strftime('%Y-%m-%d'),
                "thumbnail": "https://i.ytimg.com/vi/9bZkp7q19f0/hqdefault.jpg",
                "description": f"Official content about {query} from verified sources",
            },
            {
                "platform": "Facebook",
                "title": f"{query} viral video on Facebook",
                "url": "https://facebook.com/watch/?v=123456789",
                "stream_url": "https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_2mb.mp4",
                "views": "2.1M views",
                "duration": "2:30",
                "upload_date": (datetime.now() - timedelta(days=2)).strftime('%Y-%m-%d'),
                "thumbnail": "https://via.placeholder.com/320x180/FF6B6B/FFFFFF?text=Facebook+Video",
                "description": f"Viral {query} content trending on Facebook",
            },
            {
                "platform": "Instagram",
                "title": f"{query} Reels - Top Viral",
                "url": "https://instagram.com/reel/ABC123/",
                "stream_url": "https://sample-videos.com/zip/10/mp4/SampleVideo_640x360_2mb.mp4",
                "views": "1.8M views",
                "duration": "0:45",
                "upload_date": (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d'),
                "thumbnail": "https://via.placeholder.com/320x180/E4405F/FFFFFF?text=Instagram+Reels",
                "description": f"Top viral Reels about {query} on Instagram",
            }
        ]

        return mock_results[:max_results]

    def _get_best_thumbnail(self, entry_or_info):
        """Extract the best available thumbnail URL from video entry/info"""
        try:
            # Method 1: Check for thumbnails array
            thumbnails = entry_or_info.get('thumbnails', [])
            if thumbnails:
                # Sort by resolution (width * height) and get the best one
                best_thumbnail = max(thumbnails, key=lambda t: (t.get('width', 0) * t.get('height', 0)))
                if best_thumbnail.get('url'):
                    return best_thumbnail['url']

            # Method 2: Check for direct thumbnail URL
            thumbnail_url = entry_or_info.get('thumbnail', '')
            if thumbnail_url:
                return thumbnail_url

            # Method 3: Generate YouTube thumbnail URL from video ID
            video_id = entry_or_info.get('id', '')
            if video_id:
                # Try different YouTube thumbnail qualities
                thumbnail_urls = [
                    f"https://i.ytimg.com/vi/{video_id}/hqdefault.jpg",  # High quality
                    f"https://i.ytimg.com/vi/{video_id}/mqdefault.jpg",  # Medium quality
                    f"https://i.ytimg.com/vi/{video_id}/default.jpg",    # Default quality
                ]
                return thumbnail_urls[0]  # Return the highest quality

            # Method 4: Extract video ID from URL
            url = entry_or_info.get('url', '') or entry_or_info.get('webpage_url', '')
            if 'youtube.com/watch?v=' in url:
                video_id = url.split('v=')[1].split('&')[0]
                return f"https://i.ytimg.com/vi/{video_id}/hqdefault.jpg"
            elif 'youtu.be/' in url:
                video_id = url.split('youtu.be/')[1].split('?')[0]
                return f"https://i.ytimg.com/vi/{video_id}/hqdefault.jpg"

            # Method 5: Fallback placeholder
            return "https://via.placeholder.com/320x180/333333/FFFFFF?text=Video+Thumbnail"

        except Exception as e:
            logger.warning(f"Error extracting thumbnail: {e}")
            return "https://via.placeholder.com/320x180/333333/FFFFFF?text=No+Thumbnail"

    def search_by_duration(self, query: str, min_duration: int, max_duration: int, max_results=10):
        """Search for videos within a specific duration range"""
        return self.search(query, max_results, {
            'min_duration': min_duration,
            'max_duration': max_duration
        })

    def search_recent(self, query: str, days: int = 7, max_results=10):
        """Search for videos uploaded in the last N days"""
        min_date = (datetime.now() - timedelta(days=days)).strftime('%Y-%m-%d')
        return self.search(query, max_results, {
            'min_date': min_date,
            'sort_by': 'date'
        })

# Example usage
if __name__ == "__main__":
    search_engine = MultiPlatformSearch()
    
    # Test search
    results = search_engine.search("Imran Khan", max_results=5)
    print(f"Found {len(results)} results:")
    
    for i, result in enumerate(results, 1):
        print(f"{i}. {result['title']} ({result['platform']}) - {result['views']}")
