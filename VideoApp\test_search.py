#!/usr/bin/env python3
"""
Test script to debug video search functionality
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import yt_dlp
from utils.video_downloader import search_videos
from social.search import search_videos as social_search

def test_yt_dlp_direct():
    """Test yt-dlp directly"""
    print("=== Testing yt-dlp directly ===")
    
    ydl_opts = {
        'quiet': False,  # Enable output to see what's happening
        'no_warnings': False,
        'extract_flat': True,
        'default_search': 'ytsearch5:',
        'ignoreerrors': True,
        'no_check_certificate': True,
    }
    
    try:
        with yt_dlp.YoutubeDL(ydl_opts) as ydl:
            print("Searching for 'python tutorial'...")
            search_results = ydl.extract_info('python tutorial', download=False)
            
        print(f"Search completed!")
        print(f"Type of result: {type(search_results)}")
        
        if search_results:
            print(f"Keys in result: {search_results.keys()}")
            entries = search_results.get('entries', [])
            print(f"Number of entries: {len(entries)}")
            
            if entries:
                print("\nFirst few results:")
                for i, entry in enumerate(entries[:3]):
                    if entry:
                        print(f"  {i+1}. {entry.get('title', 'No title')} - {entry.get('url', 'No URL')}")
                    else:
                        print(f"  {i+1}. Empty entry")
            else:
                print("No entries found")
        else:
            print("No search results returned")
            
    except Exception as e:
        print(f"Error in yt-dlp search: {e}")
        import traceback
        traceback.print_exc()

def test_video_downloader():
    """Test the video_downloader module"""
    print("\n=== Testing video_downloader module ===")
    
    try:
        results = search_videos('python tutorial', platform='youtube', max_results=5)
        print(f"Search results type: {type(results)}")
        print(f"Number of results: {len(results)}")
        
        if results:
            print("\nFirst few results:")
            for i, result in enumerate(results[:3]):
                print(f"  {i+1}. {result.get('title', 'No title')}")
        else:
            print("No results from video_downloader")
            
    except Exception as e:
        print(f"Error in video_downloader search: {e}")
        import traceback
        traceback.print_exc()

def test_social_search():
    """Test the social search module"""
    print("\n=== Testing social search module ===")
    
    try:
        from social.search import Platform
        result = social_search('python tutorial', platform=Platform.YOUTUBE, max_results=5)
        print(f"Social search result type: {type(result)}")
        print(f"Success: {result.get('success', False)}")
        
        if result.get('success'):
            results = result.get('results', [])
            print(f"Number of results: {len(results)}")
            
            if results:
                print("\nFirst few results:")
                for i, result_item in enumerate(results[:3]):
                    print(f"  {i+1}. {result_item.title}")
        else:
            print(f"Error: {result.get('error', 'Unknown error')}")
            
    except Exception as e:
        print(f"Error in social search: {e}")
        import traceback
        traceback.print_exc()

def test_network_connectivity():
    """Test network connectivity to YouTube"""
    print("\n=== Testing network connectivity ===")
    
    try:
        import requests
        response = requests.get('https://www.youtube.com', timeout=10)
        print(f"YouTube connectivity: {response.status_code}")
        
        # Test yt-dlp with a specific video
        ydl_opts = {
            'quiet': True,
            'no_warnings': True,
            'extract_flat': True,
        }
        
        with yt_dlp.YoutubeDL(ydl_opts) as ydl:
            # Test with a known video URL
            info = ydl.extract_info('https://www.youtube.com/watch?v=dQw4w9WgXcQ', download=False)
            print(f"Video info extraction: Success - {info.get('title', 'Unknown')}")
            
    except Exception as e:
        print(f"Network connectivity issue: {e}")

if __name__ == "__main__":
    print("Starting video search debugging...")
    
    test_network_connectivity()
    test_yt_dlp_direct()
    test_video_downloader()
    test_social_search()
    
    print("\nDebugging completed!")
