#!/usr/bin/env python3
"""
Create a simple icon for the VideoApp
"""

from PIL import Image, ImageDraw, ImageFont
import os

def create_app_icon():
    """Create a simple app icon"""
    # Create a 256x256 image with a dark background
    size = 256
    img = Image.new('RGBA', (size, size), (45, 45, 45, 255))
    draw = ImageDraw.Draw(img)
    
    # Draw a play button triangle
    triangle_size = 80
    center_x, center_y = size // 2, size // 2
    
    # Triangle points (play button)
    points = [
        (center_x - triangle_size//2, center_y - triangle_size//2),
        (center_x - triangle_size//2, center_y + triangle_size//2),
        (center_x + triangle_size//2, center_y)
    ]
    
    # Draw the play triangle
    draw.polygon(points, fill=(0, 120, 212, 255))  # Blue color
    
    # Draw a circle around it
    circle_radius = 100
    draw.ellipse([
        center_x - circle_radius, center_y - circle_radius,
        center_x + circle_radius, center_y + circle_radius
    ], outline=(255, 255, 255, 255), width=8)
    
    # Add text "VA" for VideoApp
    try:
        # Try to use a system font
        font = ImageFont.truetype("arial.ttf", 40)
    except:
        # Fallback to default font
        font = ImageFont.load_default()
    
    text = "VA"
    bbox = draw.textbbox((0, 0), text, font=font)
    text_width = bbox[2] - bbox[0]
    text_height = bbox[3] - bbox[1]
    
    text_x = center_x - text_width // 2
    text_y = center_y + circle_radius + 20
    
    draw.text((text_x, text_y), text, fill=(255, 255, 255, 255), font=font)
    
    # Save as ICO file
    icon_path = os.path.join(os.path.dirname(__file__), 'assets', 'app_icon.ico')
    os.makedirs(os.path.dirname(icon_path), exist_ok=True)
    
    # Create multiple sizes for the ICO file
    sizes = [16, 32, 48, 64, 128, 256]
    images = []
    
    for size in sizes:
        resized = img.resize((size, size), Image.Resampling.LANCZOS)
        images.append(resized)
    
    # Save as ICO
    images[0].save(icon_path, format='ICO', sizes=[(img.width, img.height) for img in images])
    print(f"Icon created at: {icon_path}")
    
    # Also save as PNG for reference
    png_path = icon_path.replace('.ico', '.png')
    img.save(png_path, format='PNG')
    print(f"PNG version saved at: {png_path}")

if __name__ == "__main__":
    try:
        create_app_icon()
        print("Icon creation completed successfully!")
    except Exception as e:
        print(f"Error creating icon: {e}")
        print("You may need to install Pillow: pip install Pillow")
