# -*- coding: utf-8 -*-
"""
Speed control widget for video playback
"""

from core.libraries import *
from core.theme import *


class SpeedControl(QWidget):
    """Widget for controlling video playback speed"""
    
    # Signals
    speed_changed = pyqtSignal(float)  # Speed multiplier (0.5x, 1.0x, 2.0x, etc.)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.current_speed = 1.0
        self.setup_ui()
        self.connect_signals()
    
    def setup_ui(self):
        """Setup the user interface"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        
        # Title
        title_label = QLabel("Playback Speed")
        title_label.setStyleSheet(f"""
            QLabel {{
                color: {TEXT_COLOR};
                font-weight: bold;
                font-size: 12px;
                margin-bottom: 5px;
            }}
        """)
        
        # Speed slider
        slider_widget = QWidget()
        slider_layout = QHBoxLayout(slider_widget)
        slider_layout.setContentsMargins(0, 0, 0, 0)
        
        # Min speed label
        min_label = QLabel("0.25x")
        min_label.setStyleSheet(f"color: {TEXT_COLOR}; font-size: 10px;")
        
        # Speed slider
        self.speed_slider = QSlider(Qt.Horizontal)
        self.speed_slider.setRange(25, 300)  # 0.25x to 3.0x (values * 100)
        self.speed_slider.setValue(100)  # 1.0x
        self.speed_slider.setStyleSheet(f"""
            QSlider::groove:horizontal {{
                border: 1px solid {PANEL_COLOR};
                height: 8px;
                background: {BACKGROUND_COLOR};
                margin: 2px 0;
                border-radius: 4px;
            }}
            QSlider::handle:horizontal {{
                background: {ACCENT_COLOR};
                border: 1px solid {PANEL_COLOR};
                width: 18px;
                margin: -2px 0;
                border-radius: 9px;
            }}
            QSlider::sub-page:horizontal {{
                background: {ACCENT_COLOR};
                border-radius: 4px;
            }}
        """)
        
        # Max speed label
        max_label = QLabel("3.0x")
        max_label.setStyleSheet(f"color: {TEXT_COLOR}; font-size: 10px;")
        
        slider_layout.addWidget(min_label)
        slider_layout.addWidget(self.speed_slider, 1)
        slider_layout.addWidget(max_label)
        
        # Current speed display
        self.speed_display = QLabel("1.0x")
        self.speed_display.setAlignment(Qt.AlignCenter)
        self.speed_display.setStyleSheet(f"""
            QLabel {{
                color: {ACCENT_COLOR};
                font-weight: bold;
                font-size: 14px;
                background-color: {PANEL_COLOR};
                border: 1px solid {ACCENT_COLOR};
                border-radius: 5px;
                padding: 5px;
                min-width: 50px;
            }}
        """)
        
        # Preset speed buttons
        presets_widget = QWidget()
        presets_layout = QHBoxLayout(presets_widget)
        presets_layout.setContentsMargins(0, 0, 0, 0)
        
        preset_speeds = [0.5, 0.75, 1.0, 1.25, 1.5, 2.0]
        self.preset_buttons = []
        
        for speed in preset_speeds:
            btn = QPushButton(f"{speed}x")
            btn.setFixedSize(40, 25)
            btn.clicked.connect(lambda checked, s=speed: self.set_speed(s))
            btn.setStyleSheet(f"""
                QPushButton {{
                    background-color: {PANEL_COLOR};
                    color: {TEXT_COLOR};
                    border: 1px solid {ACCENT_COLOR};
                    border-radius: 3px;
                    font-size: 10px;
                }}
                QPushButton:hover {{
                    background-color: {ACCENT_COLOR};
                    color: {BACKGROUND_COLOR};
                }}
                QPushButton:pressed {{
                    background-color: {TEXT_COLOR};
                }}
            """)
            self.preset_buttons.append(btn)
            presets_layout.addWidget(btn)
        
        # Reset button
        reset_btn = QPushButton("Reset")
        reset_btn.clicked.connect(lambda: self.set_speed(1.0))
        reset_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {PANEL_COLOR};
                color: {TEXT_COLOR};
                border: 1px solid {ACCENT_COLOR};
                border-radius: 5px;
                padding: 5px 10px;
                font-size: 11px;
            }}
            QPushButton:hover {{
                background-color: {ACCENT_COLOR};
                color: {BACKGROUND_COLOR};
            }}
        """)
        
        # Add all widgets to main layout
        layout.addWidget(title_label)
        layout.addWidget(slider_widget)
        layout.addWidget(self.speed_display)
        layout.addWidget(presets_widget)
        layout.addWidget(reset_btn)
        
        # Apply overall styling
        self.setStyleSheet(f"""
            QWidget {{
                background-color: {PANEL_COLOR};
                border: 1px solid {ACCENT_COLOR};
                border-radius: 8px;
                padding: 5px;
            }}
        """)
    
    def connect_signals(self):
        """Connect widget signals to slots"""
        self.speed_slider.valueChanged.connect(self.on_slider_changed)
    
    def on_slider_changed(self, value):
        """Handle slider value changes"""
        speed = value / 100.0
        self.current_speed = speed
        self.speed_display.setText(f"{speed:.2f}x")
        self.update_preset_buttons()
        self.speed_changed.emit(speed)
    
    def set_speed(self, speed):
        """Set the speed programmatically"""
        self.current_speed = speed
        slider_value = int(speed * 100)
        self.speed_slider.setValue(slider_value)
        self.speed_display.setText(f"{speed:.2f}x")
        self.update_preset_buttons()
        self.speed_changed.emit(speed)
    
    def get_speed(self):
        """Get the current speed"""
        return self.current_speed
    
    def update_preset_buttons(self):
        """Update the appearance of preset buttons based on current speed"""
        preset_speeds = [0.5, 0.75, 1.0, 1.25, 1.5, 2.0]
        for i, btn in enumerate(self.preset_buttons):
            if abs(self.current_speed - preset_speeds[i]) < 0.01:
                # Highlight the active preset
                btn.setStyleSheet(f"""
                    QPushButton {{
                        background-color: {ACCENT_COLOR};
                        color: {BACKGROUND_COLOR};
                        border: 1px solid {ACCENT_COLOR};
                        border-radius: 3px;
                        font-size: 10px;
                        font-weight: bold;
                    }}
                """)
            else:
                # Normal appearance
                btn.setStyleSheet(f"""
                    QPushButton {{
                        background-color: {PANEL_COLOR};
                        color: {TEXT_COLOR};
                        border: 1px solid {ACCENT_COLOR};
                        border-radius: 3px;
                        font-size: 10px;
                    }}
                    QPushButton:hover {{
                        background-color: {ACCENT_COLOR};
                        color: {BACKGROUND_COLOR};
                    }}
                    QPushButton:pressed {{
                        background-color: {TEXT_COLOR};
                    }}
                """)


class SpeedControlGroupBox(QGroupBox):
    """Speed control widget wrapped in a group box (for compatibility)"""
    
    speed_changed = pyqtSignal(float)
    
    def __init__(self, parent=None):
        super().__init__("Playback Speed", parent)
        layout = QVBoxLayout(self)
        
        self.speed_control = SpeedControl()
        self.speed_control.speed_changed.connect(self.speed_changed.emit)
        
        layout.addWidget(self.speed_control)
        
        # Apply group box styling
        self.setStyleSheet(f"""
            QGroupBox {{
                color: {TEXT_COLOR};
                border: 2px solid {PANEL_COLOR};
                border-radius: 8px;
                margin-top: 10px;
                font-weight: bold;
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: {ACCENT_COLOR};
            }}
        """)
    
    def set_speed(self, speed):
        """Set the speed programmatically"""
        self.speed_control.set_speed(speed)
    
    def get_speed(self):
        """Get the current speed"""
        return self.speed_control.get_speed()
