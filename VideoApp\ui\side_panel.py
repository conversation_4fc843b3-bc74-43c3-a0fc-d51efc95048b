# -*- coding: utf-8 -*-
from core.libraries import QWidget, QVBox<PERSON>ayout, QPushButton, QLabel, Qt, QSizePolicy
import core.theme as theme   # 👈 Safe import


class SidePanel(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)

        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(6)

        # Title
        title = QLabel("🎬 Video Editor")
        title.setStyleSheet(
            f"""
            color: {theme.ACCENT_COLOR};
            font-size: 16px;
            font-family: '{theme.APP_FONT_FAMILY}';
            font-weight: bold;
            padding: 10px;
            """
        )
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)

        # Buttons
        self.goto_search = QPushButton("🔍 Search Videos")
        self.goto_download = QPushButton("⬇️ Download")
        self.goto_play = QPushButton("▶️ Play & Preview")
        self.goto_edit = QPushButton("✂️ Edit Project")
        self.goto_export = QPushButton("📤 Export Video")
        
        # Set fixed height for buttons for consistent appearance
        button_height = 45
        for btn in (self.goto_search, self.goto_download, self.goto_play, 
                   self.goto_edit, self.goto_export):
            btn.setCursor(Qt.PointingHandCursor)
            btn.setFixedHeight(button_height)
            btn.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
            btn.setStyleSheet(
                f"""
                QPushButton {{
                    background-color: {theme.PANEL_COLOR};
                    color: {theme.TEXT_COLOR};
                    padding: 8px 12px;
                    border-radius: 6px;
                    border: 1px solid #374151;
                    font-family: '{theme.APP_FONT_FAMILY}';
                    text-align: left;
                    font-size: 14px;
                }}
                QPushButton:hover {{
                    background-color: {theme.ACCENT_COLOR};
                    color: {theme.BACKGROUND_COLOR};
                }}
                QPushButton:pressed {{
                    background-color: {theme.ACCENT_COLOR_DARK};
                }}
                """
            )
            layout.addWidget(btn)

        layout.addStretch(1)
        
        # Add version info
        version_label = QLabel("v1.0.0")
        version_label.setAlignment(Qt.AlignCenter)
        version_label.setStyleSheet(f"color: {theme.TEXT_SECONDARY}; font-size: 10px;")
        layout.addWidget(version_label)

        # Panel background
        self.setStyleSheet(f"background-color: {theme.BACKGROUND_COLOR}; border-right: 1px solid {theme.BORDER_COLOR};")
