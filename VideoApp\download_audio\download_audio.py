# -*- coding: utf-8 -*-
# Consolidated audio download module for VideoApp

import os
from typing import Optional
from utils.video_downloader import download_audio_only
from utils.audio_utils import validate_audio_format, normalize_audio
from utils.exceptions import DownloadError, InvalidFormatError

def download_audio(url: str, audio_format: str = 'mp3', 
                  bitrate: Optional[int] = None, 
                  output_dir: Optional[str] = None) -> str:
    """
    Download audio from a video URL in the specified format.
    
    Args:
        url: URL of the video to extract audio from
        audio_format: Desired audio format ('mp3', 'aac', 'wav')
        bitrate: Optional bitrate for the audio (e.g., 128, 192, 320 for MP3)
        output_dir: Optional directory to save the audio file
    
    Returns:
        Path to the downloaded audio file
    
    Raises:
        InvalidFormatError: If the requested audio format is not supported
        DownloadError: If the download fails
    """
    # Validate the requested format
    if not validate_audio_format(audio_format):
        raise InvalidFormatError(f"Unsupported audio format: {audio_format}")
    
    try:
        # Download the audio
        audio_path = download_audio_only(
            url, 
            audio_format=audio_format, 
            bitrate=bitrate,
            output_dir=output_dir
        )
        
        # Normalize audio levels for better editing experience
        normalized_path = normalize_audio(audio_path)
        
        # Return the path to the normalized audio file
        return normalized_path
        
    except Exception as e:
        # Clean up partially downloaded files
        if 'audio_path' in locals() and os.path.exists(audio_path):
            os.remove(audio_path)
        raise DownloadError(f"Failed to download audio: {str(e)}")

# Convenience functions for specific formats
def download_mp3(url: str, bitrate: int = 192) -> str:
    """Download audio in MP3 format."""
    return download_audio(url, audio_format='mp3', bitrate=bitrate)

def download_aac(url: str, bitrate: int = 128) -> str:
    """Download audio in AAC format."""
    return download_audio(url, audio_format='aac', bitrate=bitrate)

def download_wav(url: str) -> str:
    """Download audio in WAV format (lossless)."""
    return download_audio(url, audio_format='wav')
