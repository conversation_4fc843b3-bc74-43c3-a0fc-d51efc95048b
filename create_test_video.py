#!/usr/bin/env python3
"""
Create a simple test video for testing the video editor
"""

import os
import sys

def create_test_video():
    """Create a simple test video using moviepy"""
    try:
        import moviepy.editor as mp
        import numpy as np
        
        # Create a simple colored video clip
        duration = 10  # 10 seconds
        fps = 24
        size = (640, 480)
        
        def make_frame(t):
            # Create a simple animated background
            color_intensity = int(128 + 127 * np.sin(2 * np.pi * t / 2))
            frame = np.full((size[1], size[0], 3), [color_intensity, 100, 200], dtype=np.uint8)
            return frame
        
        # Create video clip
        clip = mp.VideoClip(make_frame, duration=duration)
        clip = clip.set_fps(fps)
        
        # Add some text
        txt_clip = mp.TextClip("Test Video for BISH Editor", 
                              fontsize=50, 
                              color='white',
                              font='Arial-Bold')
        txt_clip = txt_clip.set_position('center').set_duration(duration)
        
        # Composite video
        final_clip = mp.CompositeVideoClip([clip, txt_clip])
        
        # Export
        output_path = "test_videos/sample_test_video.mp4"
        os.makedirs("test_videos", exist_ok=True)
        
        print(f"Creating test video: {output_path}")
        final_clip.write_videofile(output_path, 
                                  codec='libx264',
                                  audio_codec='aac',
                                  temp_audiofile='temp-audio.m4a',
                                  remove_temp=True)
        
        print(f"✅ Test video created: {output_path}")
        return output_path
        
    except ImportError:
        print("❌ MoviePy not available. Creating a placeholder file.")
        # Create a placeholder text file
        output_path = "test_videos/README_TEST_VIDEO.txt"
        os.makedirs("test_videos", exist_ok=True)
        
        with open(output_path, 'w') as f:
            f.write("""
Test Video Information
=====================

To test the video editor, you can:

1. Download a sample video from the internet
2. Use any MP4, AVI, MOV, or MKV file you have
3. Try importing the video using the Import button in the toolbar

The video editor supports these formats:
- MP4 (recommended)
- AVI
- MOV
- MKV
- WMV
- FLV
- WebM
- M4V
- 3GP

Instructions:
1. Click the "📁 Import" button in the toolbar
2. Select your video file
3. The video should load in the player
4. Use the playback controls to play/pause
5. The video will also be added to the timeline

If you encounter any issues, check the console output for error messages.
""")
        
        print(f"✅ Created instructions file: {output_path}")
        return output_path

if __name__ == "__main__":
    create_test_video()
