#!/usr/bin/env python3
"""
Test video import functionality to debug the resource error
"""

import sys
import os
sys.path.append('VideoApp')

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QLabel, QFileDialog, QMessageBox
from PyQt5.QtCore import QTimer
from ui.video_player_widget import VideoPlayerWidget
from ui.premiere_interface import PremiereInterface

class VideoImportTestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("🧪 Video Import Debug Test")
        self.setGeometry(100, 100, 800, 600)
        
        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # Title
        title = QLabel("🎯 Video Import Debug Test")
        title.setStyleSheet("font-size: 18px; font-weight: bold; color: #0078d4; padding: 10px;")
        layout.addWidget(title)
        
        # Test buttons
        self.btn_test_player = QPushButton("🎬 Test VideoPlayerWidget")
        self.btn_test_player.clicked.connect(self.test_video_player)
        layout.addWidget(self.btn_test_player)
        
        self.btn_test_premiere = QPushButton("🎭 Test PremiereInterface")
        self.btn_test_premiere.clicked.connect(self.test_premiere_interface)
        layout.addWidget(self.btn_test_premiere)
        
        self.btn_browse_file = QPushButton("📁 Browse and Test File")
        self.btn_browse_file.clicked.connect(self.browse_and_test_file)
        layout.addWidget(self.btn_browse_file)
        
        # Status area
        self.status_label = QLabel("Ready to test video import...")
        self.status_label.setStyleSheet("color: #888; padding: 10px; border: 1px solid #555; background-color: #2b2b2b;")
        layout.addWidget(self.status_label)
        
        # Initialize components
        self.video_player = None
        self.premiere_interface = None
        
    def log(self, message):
        """Log message to status"""
        print(message)
        self.status_label.setText(message)
        QApplication.processEvents()
        
    def test_video_player(self):
        """Test VideoPlayerWidget directly"""
        self.log("🧪 Testing VideoPlayerWidget...")
        
        try:
            # Create video player
            self.video_player = VideoPlayerWidget()
            self.log("✅ VideoPlayerWidget created successfully")
            
            # Connect error signal
            self.video_player.error_occurred.connect(self.on_player_error)
            
            # Test with a dummy file to see what happens
            test_file = "nonexistent_test.mp4"
            self.log(f"🔍 Testing load with dummy file: {test_file}")
            self.video_player.load(test_file)
            
        except Exception as e:
            self.log(f"❌ VideoPlayerWidget test failed: {str(e)}")
            import traceback
            traceback.print_exc()
    
    def test_premiere_interface(self):
        """Test PremiereInterface video loading"""
        self.log("🧪 Testing PremiereInterface...")
        
        try:
            # Create premiere interface
            self.premiere_interface = PremiereInterface()
            self.log("✅ PremiereInterface created successfully")
            
            # Test video loading method
            if hasattr(self.premiere_interface, 'load_video_from_file'):
                self.log("✅ load_video_from_file method exists")
            else:
                self.log("❌ load_video_from_file method missing")
                
        except Exception as e:
            self.log(f"❌ PremiereInterface test failed: {str(e)}")
            import traceback
            traceback.print_exc()
    
    def browse_and_test_file(self):
        """Browse for a real file and test loading"""
        self.log("📁 Opening file browser...")
        
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "Select Video File for Testing",
            "",
            "Video Files (*.mp4 *.avi *.mov *.mkv *.wmv *.flv *.webm *.m4v *.3gp);;All Files (*)"
        )
        
        if file_path:
            self.log(f"📄 Selected file: {os.path.basename(file_path)}")
            self.test_file_loading(file_path)
        else:
            self.log("❌ No file selected")
    
    def test_file_loading(self, file_path):
        """Test loading a specific file"""
        self.log(f"🧪 Testing file loading: {file_path}")
        
        # Check file properties
        if not os.path.exists(file_path):
            self.log("❌ File does not exist")
            return
            
        if not os.access(file_path, os.R_OK):
            self.log("❌ File is not readable")
            return
            
        file_size = os.path.getsize(file_path)
        self.log(f"📊 File size: {file_size} bytes")
        
        if file_size == 0:
            self.log("❌ File is empty")
            return
        
        # Test with VideoPlayerWidget
        if not self.video_player:
            self.test_video_player()
        
        if self.video_player:
            self.log("🎬 Testing with VideoPlayerWidget...")
            try:
                self.video_player.load(file_path)
                self.log("✅ VideoPlayerWidget load called successfully")
                
                # Wait a bit and check player state
                QTimer.singleShot(2000, lambda: self.check_player_state(file_path))
                
            except Exception as e:
                self.log(f"❌ VideoPlayerWidget load failed: {str(e)}")
        
        # Test with PremiereInterface
        if not self.premiere_interface:
            self.test_premiere_interface()
            
        if self.premiere_interface:
            self.log("🎭 Testing with PremiereInterface...")
            try:
                self.premiere_interface.load_video_from_file(file_path)
                self.log("✅ PremiereInterface load called successfully")
                
            except Exception as e:
                self.log(f"❌ PremiereInterface load failed: {str(e)}")
    
    def check_player_state(self, file_path):
        """Check the player state after loading"""
        if self.video_player and hasattr(self.video_player, 'player'):
            try:
                player = self.video_player.player
                state = player.state()
                media_status = player.mediaStatus()
                error = player.error()
                
                self.log(f"🔍 Player State: {state}")
                self.log(f"🔍 Media Status: {media_status}")
                self.log(f"🔍 Player Error: {error}")
                
                if error != 0:  # QMediaPlayer.NoError = 0
                    error_string = player.errorString()
                    self.log(f"❌ Player Error String: {error_string}")
                    
            except Exception as e:
                self.log(f"❌ Error checking player state: {str(e)}")
    
    def on_player_error(self, error_message):
        """Handle player error signal"""
        self.log(f"🚨 Player Error Signal: {error_message}")

if __name__ == "__main__":
    app = QApplication(sys.argv)
    
    # Set dark theme
    app.setStyleSheet("""
        QMainWindow {
            background-color: #2b2b2b;
            color: white;
        }
        QWidget {
            background-color: #2b2b2b;
            color: white;
        }
        QPushButton {
            background-color: #404040;
            color: white;
            border: 1px solid #555;
            padding: 10px 15px;
            border-radius: 5px;
            font-weight: bold;
        }
        QPushButton:hover {
            background-color: #505050;
        }
        QLabel {
            color: white;
        }
    """)
    
    window = VideoImportTestWindow()
    window.show()
    
    print("Video import debug window opened.")
    print("Click buttons to test different components...")
    
    sys.exit(app.exec_())
